import type { PayloadAction } from "@reduxjs/toolkit";
import { createSlice } from "@reduxjs/toolkit";
import exerciseCatalog, { Exercise, ExerciseCatalog } from "../../Seeds/ExerciseCatalog";

// Interface for the exercise catalog slice state
interface ExerciseCatalogSlice {
  catalog: ExerciseCatalog;
  customExercises: Exercise[]; // User-created exercises
  searchQuery: string;
  selectedCategory: string | null;
  isLoading: boolean;
}

// Define the initial state using that type
const initialState: ExerciseCatalogSlice = {
  catalog: exerciseCatalog, // Load initial exercises from seed data
  customExercises: [], // Start with no custom exercises
  searchQuery: "",
  selectedCategory: null,
  isLoading: false,
};

export const exerciseCatalogSlice = createSlice({
  name: "exerciseCatalog",
  initialState,
  reducers: {
    // Add a new custom exercise
    addCustomExercise: (state, action: PayloadAction<Exercise>) => {
      state.customExercises.push(action.payload);
    },

    // Update an existing custom exercise
    updateCustomExercise: (state, action: PayloadAction<Exercise>) => {
      const index = state.customExercises.findIndex(
        (exercise) => exercise.id === action.payload.id
      );
      if (index !== -1) {
        state.customExercises[index] = action.payload;
      }
    },

    // Remove a custom exercise
    removeCustomExercise: (state, action: PayloadAction<string>) => {
      state.customExercises = state.customExercises.filter(
        (exercise) => exercise.id !== action.payload
      );
    },

    // Set search query for filtering exercises
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },

    // Set selected category for filtering
    setSelectedCategory: (state, action: PayloadAction<string | null>) => {
      state.selectedCategory = action.payload;
    },

    // Set loading state
    setIsLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Reset search and filters
    resetFilters: (state) => {
      state.searchQuery = "";
      state.selectedCategory = null;
    },

    // Add exercise to a specific category in the catalog
    addExerciseToCategory: (
      state,
      action: PayloadAction<{ categoryName: string; exercise: Exercise }>
    ) => {
      const { categoryName, exercise } = action.payload;
      const categoryIndex = state.catalog.categories.findIndex(
        (category) => category.bodyPart === categoryName
      );

      if (categoryIndex !== -1) {
        state.catalog.categories[categoryIndex].exercises.push(exercise);
      } else {
        // Create new category if it doesn't exist
        state.catalog.categories.push({
          bodyPart: categoryName,
          exercises: [exercise],
        });
      }
    },

    // Reset the entire catalog to initial state
    resetCatalog: (state) => {
      state.catalog = exerciseCatalog;
      state.customExercises = [];
      state.searchQuery = "";
      state.selectedCategory = null;
      state.isLoading = false;
    },
  },
});

export const {
  addCustomExercise,
  updateCustomExercise,
  removeCustomExercise,
  setSearchQuery,
  setSelectedCategory,
  setIsLoading,
  resetFilters,
  addExerciseToCategory,
  resetCatalog,
} = exerciseCatalogSlice.actions;

export default exerciseCatalogSlice.reducer;

// Selectors for getting computed data
export const selectAllExercises = (state: { exerciseCatalog: ExerciseCatalogSlice }) => {
  const catalogExercises = state.exerciseCatalog.catalog.categories.flatMap(
    (category) => category.exercises
  );
  return [...catalogExercises, ...state.exerciseCatalog.customExercises];
};

export const selectFilteredExercises = (state: { exerciseCatalog: ExerciseCatalogSlice }) => {
  const allExercises = selectAllExercises(state);
  const { searchQuery, selectedCategory } = state.exerciseCatalog;

  return allExercises.filter((exercise) => {
    const matchesSearch = searchQuery
      ? exercise.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        exercise.mainMuscle.toLowerCase().includes(searchQuery.toLowerCase()) ||
        exercise.equipment.toLowerCase().includes(searchQuery.toLowerCase())
      : true;

    const matchesCategory = selectedCategory
      ? exercise.mainMuscle === selectedCategory ||
        exercise.targetMuscles.includes(selectedCategory)
      : true;

    return matchesSearch && matchesCategory;
  });
};

export const selectExercisesByCategory = (state: { exerciseCatalog: ExerciseCatalogSlice }) => {
  const { catalog, customExercises } = state.exerciseCatalog;

  // Combine catalog exercises with custom exercises
  const categoriesWithCustom = [...catalog.categories];

  // Add custom exercises to appropriate categories or create new ones
  customExercises.forEach((exercise) => {
    const categoryIndex = categoriesWithCustom.findIndex(
      (category) => category.bodyPart === exercise.mainMuscle
    );

    if (categoryIndex !== -1) {
      categoriesWithCustom[categoryIndex].exercises.push(exercise);
    } else {
      categoriesWithCustom.push({
        bodyPart: exercise.mainMuscle,
        exercises: [exercise],
      });
    }
  });

  return categoriesWithCustom;
};

export const selectCustomExercises = (state: { exerciseCatalog: ExerciseCatalogSlice }) => {
  return state.exerciseCatalog.customExercises;
};

export const selectExerciseById = (state: { exerciseCatalog: ExerciseCatalogSlice }, exerciseId: string) => {
  const allExercises = selectAllExercises(state);
  return allExercises.find((exercise) => exercise.id === exerciseId);
};
