import React, { FC, useMemo, useState } from "react";
import {
  FlatList,
  Image,
  Pressable,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import ICONS from "../../../Assets/Icons";
import CustomIcon from "../../../Components/CustomIcon";
import PickerComponent from "../../../Components/CustomPIcker";
import { CustomText } from "../../../Components/CustomText";
import PrimaryButton from "../../../Components/PrimaryButton";
import {
  SetDetail,
  WorkoutDay,
  workoutHistory,
} from "../../../Seeds/TrainingPLans";
import COLORS from "../../../Utilities/Colors";
import {
  calculate1RM,
  extractDistance,
  extractNumericValue,
  extractReps,
  extractTime,
} from "../../../Utilities/Helpers";
import {
  horizontalScale,
  hp,
  verticalScale,
  wp,
} from "../../../Utilities/Metrics";
import { MuscleData } from "./ExerciseView";

const tabData = [
  { label: "Sets", value: 1 },
  { label: "Details", value: 2 },
  { label: "History", value: 3 },
];

const setsTabData = [
  { label: "Last Workout", value: 1 },
  { label: "Last Exercise", value: 2 },
  { label: "Max Weight", value: 3 },
  { label: "Max Time", value: 4 },
  { label: "1RM Max", value: 6 },
];

const ExerciseDetails: FC<{
  exerciseData: any;
  showAddSetUi: boolean;
  setShowAddSetUi: any;
}> = ({ exerciseData, showAddSetUi, setShowAddSetUi }) => {
  const [activeTab, setActiveTab] = useState(1);
  const [showInstructions, setShowInstructions] = useState(true);

  const [setsTab, setSetsTab] = useState(1);

  const muscleData = useMemo(() => {
    const muscleCount: { [key: string]: number } = {};
    let totalMuscleMentions = 0;

    exerciseData.targetMuscles?.forEach((muscle: any) => {
      muscleCount[muscle] = (muscleCount[muscle] || 0) + 1;
      totalMuscleMentions++;
    });

    const muscles: MuscleData[] = Object.keys(muscleCount).map((muscle) => ({
      name: muscle,
      percentage: totalMuscleMentions
        ? Math.round((muscleCount[muscle] / totalMuscleMentions) * 100)
        : 0,
    }));

    return muscles.sort((a, b) => b.percentage - a.percentage);
  }, [exerciseData]);

  const renderTabs = () => {
    return (
      <View style={styles.tabContainer}>
        {tabData.map((tab) => (
          <Pressable
            key={tab.value}
            onPress={() => setActiveTab(tab.value)}
            style={[
              styles.tabButton,
              {
                backgroundColor:
                  activeTab === tab.value ? COLORS.yellow : "transparent",
              },
            ]}
          >
            <CustomText fontSize={14} fontFamily="medium">
              {tab.label}
            </CustomText>
          </Pressable>
        ))}
      </View>
    );
  };

  const renderDetailsTab = () => {
    return (
      <ScrollView
        contentContainerStyle={{ alignItems: "center", gap: verticalScale(20) }}
        style={{
          paddingHorizontal: horizontalScale(10),
          gap: verticalScale(20),
          flex: 1,
        }}
      >
        <View style={styles.tagContainer}>
          {exerciseData?.targetMuscles &&
            exerciseData.targetMuscles.map((tag: string, index: number) => {
              return (
                <CustomText
                  key={index}
                  style={styles.tag}
                  fontFamily="italicBold"
                  fontSize={14}
                  color={COLORS.whiteTail}
                >
                  {tag}
                </CustomText>
              );
            })}
        </View>
        <FlatList
          data={Array.from({ length: 6 })}
          horizontal
          contentContainerStyle={{ gap: horizontalScale(10) }}
          renderItem={() => {
            return (
              <View style={{ flexDirection: "row", gap: horizontalScale(10) }}>
                <Image
                  source={{
                    uri: "https://images.unsplash.com/photo-1483721310020-03333e577078?q=80&w=1528&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                  }}
                  style={{
                    height: 120,
                    width: 120,
                    resizeMode: "cover",
                    borderRadius: 10,
                  }}
                />
              </View>
            );
          }}
        />
        <Image
          source={{ uri: exerciseData.image }}
          style={{ height: hp(40), width: wp(90), borderRadius: 20 }}
        />

        <View
          style={{
            paddingVertical: verticalScale(15),
            borderRadius: 10,
            width: wp(90),
            gap: verticalScale(20),
          }}
        >
          <TouchableOpacity
            onPress={() => setShowInstructions(!showInstructions)}
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              gap: horizontalScale(10),
              width: "100%",
            }}
          >
            <CustomText
              fontFamily="extraBold"
              fontSize={24}
              style={{ flex: 1 }}
            >
              Instructions
            </CustomText>
            <CustomText fontFamily="extraBold" fontSize={20}>
              {showInstructions ? "-" : "+"}{" "}
            </CustomText>
          </TouchableOpacity>
          {showInstructions && (
            <View style={{ gap: verticalScale(3) }}>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Stand with your feet shoulder-width apart, toes slightly
                pointed out.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Keep your chest up, shoulders back, and core engaged.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Look straight ahead to maintain a neutral neck position.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Take a deep breath, brace your core, and push your hips back
                as if sitting into a chair.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Bend your knees and lower yourself until your thighs are at
                least parallel to the floor, or as low as your flexibility
                allows.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Keep your weight on your heels and midfoot, ensuring your
                knees track over your toes without caving inward.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - At the bottom position, maintain a straight back and engaged
                core.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Drive through your heels to return to a standing position,
                straightening your hips and knees simultaneously.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Exhale as you rise, keeping your chest lifted throughout.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Avoid rounding your back, letting your knees cave inward, or
                lifting your heels off the ground.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Perform 3-5 sets, adjusting reps and weight based on your
                goals.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - At the bottom position, maintain a straight back and engaged
                core.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Stop if you experience any pain or discomfort.
              </CustomText>
              <CustomText
                fontSize={14}
                fontFamily="medium"
                color={COLORS.whiteTail}
              >
                - Warm up before and stretch after for safety.
              </CustomText>
            </View>
          )}
        </View>

        <View
          style={{
            width: wp(95),
            borderRadius: 20,
            paddingHorizontal: horizontalScale(10),
            paddingVertical: verticalScale(20),
            alignItems: "center",
            gap: verticalScale(30),
          }}
        >
          <View style={{ width: "100%" }}>
            <CustomText
              fontFamily="bold"
              fontSize={24}
              style={{ textAlign: "left" }}
            >
              Main Muscle
            </CustomText>
          </View>
          <CustomIcon Icon={ICONS.dummyTargetMuscle} height={300} width={300} />
        </View>
        <View
          style={{
            width: wp(95),
            borderRadius: 20,
            paddingHorizontal: horizontalScale(10),
            paddingVertical: verticalScale(20),
            alignItems: "center",
            gap: verticalScale(30),
          }}
        >
          <View style={{ width: "100%" }}>
            <CustomText
              fontFamily="bold"
              fontSize={24}
              style={{ textAlign: "left" }}
            >
              Secondary Muscle
            </CustomText>
          </View>
          <CustomIcon Icon={ICONS.dummyTargetMuscle} height={300} width={300} />
        </View>
      </ScrollView>
    );
  };

  const renderHistory = () => {
    // Function to extract all exercises with their dates and details
    const getAllExercisesHistory = (
      workoutHistory: WorkoutDay[]
    ): { name: string; date: string; details: SetDetail[] }[] => {
      const result: { name: string; date: string; details: SetDetail[] }[] = [];

      workoutHistory.forEach((day) => {
        day.exercises.forEach((exercise) => {
          result.push({
            name: exercise.name,
            date: day.date,
            details: exercise.details,
          });
        });
      });

      result.sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      return result;
    };

    const allExercisesHistory = getAllExercisesHistory(workoutHistory);

    return (
      <View
        style={{
          rowGap: verticalScale(10),
          flex: 1,
        }}
      >
        <FlatList
          data={allExercisesHistory}
          contentContainerStyle={{ gap: verticalScale(10) }}
          renderItem={({ item }) => {
            return (
              <View
                style={{
                  backgroundColor: COLORS.lightBrown,
                  padding: verticalScale(10),
                  borderRadius: 10,
                  gap: verticalScale(10),
                  borderWidth: 1,
                  borderColor: COLORS.white,
                }}
              >
                <View
                  style={{
                    flexDirection: "row",
                    gap: horizontalScale(10),
                    alignItems: "center",
                  }}
                >
                  <View
                    style={{
                      width: 35,
                      height: 35,
                      justifyContent: "center",
                      alignItems: "center",
                      backgroundColor: COLORS.darkPink,
                      borderRadius: 100,
                    }}
                  >
                    <CustomIcon
                      Icon={ICONS.CalendarWithDumbellIcon}
                      height={27}
                      width={27}
                    />
                  </View>
                  <View
                    style={{
                      gap: verticalScale(5),
                    }}
                  >
                    <CustomText
                      fontFamily="semiBold"
                      fontSize={14}
                      color={COLORS.yellow}
                    >
                      {item.name}
                    </CustomText>
                    <CustomText fontFamily="italic" fontSize={12}>
                      {item.date}
                    </CustomText>
                  </View>
                </View>

                <View
                  style={{
                    gap: verticalScale(6),
                    paddingHorizontal: horizontalScale(10),
                  }}
                >
                  {item.details.map((exercise, index) => (
                    <View
                      key={exercise.time + index.toString()}
                      style={{
                        flexDirection: "row",
                        gap: horizontalScale(5),
                      }}
                    >
                      <CustomText
                        fontFamily="medium"
                        fontSize={13}
                        color={COLORS.whiteTail}
                      >
                        {`${index + 1}. ${exercise.weight} ${exercise.reps} ${
                          exercise.time
                        } x${exercise.count}`}
                      </CustomText>
                    </View>
                  ))}
                </View>
              </View>
            );
          }}
        />
      </View>
    );
  };

  // State for managing the selected difficulty for new sets
  const [selectedDifficulty, setSelectedDifficulty] = useState<
    "Warmup" | "Easy" | "Medium" | "Hard"
  >("Medium");

  // State for managing newly added sets that should appear at the top
  const [newlyAddedSets, setNewlyAddedSets] = useState<SetDetail[]>([]);

  // State for tracking current picker values
  const [currentPickerValues, setCurrentPickerValues] = useState({
    reps: "6",
    distance: "100m",
    weight: "6kg",
    time: "6m",
  });

  // Function to add a new set
  const handleAddSet = () => {
    const newSet: SetDetail = {
      weight: currentPickerValues.weight,
      reps: currentPickerValues.reps,
      time: currentPickerValues.time,
      distance: currentPickerValues.distance, // Add distance field
      count: 1, // Default count
      difficulty: selectedDifficulty,
      dropSets: [], // Initialize empty drop sets array
    };

    // Add the new set to the beginning of the array (top of the list)
    setNewlyAddedSets((prev) => [newSet, ...prev]);

    // Close the AddSetUI
    setShowAddSetUi(false);
  };

  // Function to add a drop set to the last created set
  const handleAddDropSet = () => {
    // This function should only be called when there are existing sets
    // The button is disabled when newlyAddedSets.length === 0
    if (newlyAddedSets.length === 0) {
      console.warn(
        "Cannot add drop set: No sets available. Add a regular set first."
      );
      return;
    }

    const dropSet: SetDetail = {
      weight: currentPickerValues.weight,
      reps: currentPickerValues.reps,
      time: currentPickerValues.time,
      distance: currentPickerValues.distance, // Add distance field
      count: 1,
      difficulty: selectedDifficulty,
      isDropSet: true,
      parentSetIndex: 0, // Always add to the first (most recent) set
    };

    // Add the drop set to the first (most recent) set's dropSets array
    setNewlyAddedSets((prev) => {
      const updatedSets = [...prev];
      if (updatedSets[0]) {
        updatedSets[0] = {
          ...updatedSets[0],
          dropSets: [...(updatedSets[0].dropSets || []), dropSet],
        };
      }
      return updatedSets;
    });

    // Close the AddSetUI
    setShowAddSetUi(false);
  };

  // Function to prepare sets for display (no flattening, keep drop sets nested)
  const prepareSetsForDisplay = (sets: SetDetail[]) => {
    return sets.map((set, index) => ({
      ...set,
      displayIndex: index + 1,
      isMainSet: true,
      mainSetIndex: index,
    }));
  };

  // Get history sets based on the selected tab
  const getHistorySets = (): SetDetail[] | null => {
    // If no exercise data is provided, return null
    if (!exerciseData || !exerciseData.name) return null;

    const exerciseName = exerciseData.name;

    // Helper function to check if exercise names match (more flexible matching)
    const isExerciseMatch = (historyName: string, currentName: string) => {
      // Convert both names to lowercase for case-insensitive comparison
      const historyLower = historyName.toLowerCase();
      const currentLower = currentName.toLowerCase();

      // Direct match
      if (historyLower === currentLower) return true;

      // Check if one contains the other
      if (
        historyLower.includes(currentLower) ||
        currentLower.includes(historyLower)
      )
        return true;

      // Check for common variations (e.g., "Squat" vs "Squats")
      if (historyLower.replace(/s$/, "") === currentLower.replace(/s$/, ""))
        return true;

      return false;
    };

    // Filter workout history to find exercises with matching names
    const exerciseHistory = workoutHistory.flatMap((day) => {
      return day.exercises
        .filter((exercise) => isExerciseMatch(exercise.name, exerciseName))
        .map((exercise) => ({
          date: day.date,
          workoutId: day.date, // Using date as workoutId for simplicity
          exercise,
        }));
    });

    // If no history found, return null
    if (exerciseHistory.length === 0) return null;

    // Sort by date (newest first)
    exerciseHistory.sort(
      (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
    );

    switch (setsTab) {
      case 1: // Last Workout
        // Return the sets from the most recent workout with newly added sets at the top
        const lastWorkoutSets = exerciseHistory[0]?.exercise.details || [];
        return [...newlyAddedSets, ...lastWorkoutSets];

      case 2: // Last Exercise
        // Return the sets from the most recent exercise with newly added sets at the top
        const lastExerciseSets = exerciseHistory[0]?.exercise.details || [];
        return [...newlyAddedSets, ...lastExerciseSets];

      case 3: // Max Weight
        // Calculate total weight for each exercise (sum of weight * reps for all sets)
        const exercisesByTotalWeight = exerciseHistory.map((item) => {
          const totalWeight = item.exercise.details.reduce((sum, set) => {
            return (
              sum +
              extractNumericValue(set.weight) *
                extractReps(set.reps) *
                set.count
            );
          }, 0);
          return { ...item, totalWeight };
        });

        // Sort by total weight (highest first)
        exercisesByTotalWeight.sort((a, b) => b.totalWeight - a.totalWeight);

        // Return the sets from the exercise with the highest total weight with newly added sets at the top
        const maxWeightSets = exercisesByTotalWeight[0]?.exercise.details || [];
        return [...newlyAddedSets, ...maxWeightSets];

      case 4: // Max Time
        // Calculate total time for each exercise
        const exercisesByTotalTime = exerciseHistory.map((item) => {
          const totalTime = item.exercise.details.reduce((sum, set) => {
            return sum + extractTime(set.time) * set.count;
          }, 0);
          return { ...item, totalTime };
        });

        // Sort by total time (highest first)
        exercisesByTotalTime.sort((a, b) => b.totalTime - a.totalTime);

        // Return the sets from the exercise with the highest total time with newly added sets at the top
        const maxTimeSets = exercisesByTotalTime[0]?.exercise.details || [];
        return [...newlyAddedSets, ...maxTimeSets];

      case 5: // Max Distance (not in the tabs but mentioned in requirements)
        // Calculate total distance for each exercise
        const exercisesByTotalDistance = exerciseHistory.map((item) => {
          const totalDistance = item.exercise.details.reduce((sum, set) => {
            // Assuming distance is stored in the reps field with a format like "123m"
            return sum + extractDistance(set.reps) * set.count;
          }, 0);
          return { ...item, totalDistance };
        });

        // Sort by total distance (highest first)
        exercisesByTotalDistance.sort(
          (a, b) => b.totalDistance - a.totalDistance
        );

        // Return the sets from the exercise with the highest total distance with newly added sets at the top
        const maxDistanceSets =
          exercisesByTotalDistance[0]?.exercise.details || [];
        return [...newlyAddedSets, ...maxDistanceSets];

      case 6: // 1RM (not in the tabs but mentioned in requirements)
        // Calculate 1RM for each set in each exercise
        const exercisesWith1RM = exerciseHistory.flatMap((item) => {
          return item.exercise.details.map((set) => {
            const oneRM = calculate1RM(set.weight, set.reps);
            return { ...item, set, oneRM };
          });
        });

        // Sort by 1RM (highest first)
        exercisesWith1RM.sort((a, b) => b.oneRM - a.oneRM);

        // Return the set with the highest 1RM with newly added sets at the top
        const max1RMSets =
          exercisesWith1RM.length > 0 ? [exercisesWith1RM[0].set] : [];
        return [...newlyAddedSets, ...max1RMSets];

      default:
        return null;
    }
  };

  const renderSets = () => {
    // Get history sets based on the selected tab
    const historySets = getHistorySets();

    return showAddSetUi ? (
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          gap: verticalScale(40),
        }}
      >
        <PickerComponent
          difficulty={selectedDifficulty}
          onValuesChange={setCurrentPickerValues}
        />
        {/* Difficulty selector */}
        <View style={styles.difficultyContainer}>
          {["Warmup", "Easy", "Medium", "Hard"].map((difficulty) => (
            <TouchableOpacity
              key={difficulty}
              style={[
                styles.difficultyButton,
                {
                  backgroundColor:
                    selectedDifficulty === difficulty
                      ? difficulty === "Warmup"
                        ? "#777777"
                        : difficulty === "Easy"
                        ? "#28A745"
                        : difficulty === "Medium"
                        ? "#FFC107"
                        : "#DC3545"
                      : "transparent",
                  borderColor:
                    difficulty === "Warmup"
                      ? "#777777"
                      : difficulty === "Easy"
                      ? "#28A745"
                      : difficulty === "Medium"
                      ? "#FFC107"
                      : "#DC3545",
                },
              ]}
              onPress={() =>
                setSelectedDifficulty(
                  difficulty as "Warmup" | "Easy" | "Medium" | "Hard"
                )
              }
            >
              <CustomText
                fontSize={12}
                fontFamily="medium"
                color={
                  selectedDifficulty === difficulty
                    ? COLORS.black
                    : difficulty === "Warmup"
                    ? "#777777"
                    : COLORS.white
                }
              >
                {difficulty}
              </CustomText>
            </TouchableOpacity>
          ))}
        </View>
        <View
          style={{
            width: wp(80),
            flexDirection: "row",
            justifyContent: "space-between",
            paddingHorizontal: horizontalScale(10),
            alignSelf: "center",
          }}
        >
          {/* Add buttons */}

          <PrimaryButton
            title="Add Drop Set"
            onPress={handleAddDropSet}
            disabled={newlyAddedSets.length === 0} // Disable if no sets exist
            isFullWidth={false}
            style={{
              alignSelf: "flex-end",
              paddingVertical: verticalScale(7),
              paddingHorizontal: horizontalScale(15),
              borderRadius: verticalScale(10),
              opacity: newlyAddedSets.length === 0 ? 0.5 : 1, // Visual feedback for disabled state
            }}
            backgroundColor="#3683DC"
          />
          <PrimaryButton
            title="Add Set"
            onPress={handleAddSet}
            isFullWidth={false}
            style={{
              alignSelf: "flex-end",
              paddingVertical: verticalScale(7),
              paddingHorizontal: horizontalScale(22),
              borderRadius: verticalScale(10),
            }}
          />
        </View>
      </View>
    ) : (
      <View style={{ flex: 1, gap: verticalScale(10) }}>
        {/* <View
          style={{ width: wp(100), paddingHorizontal: horizontalScale(10) }}
        >
          <FlatList
            horizontal
            data={muscleData}
            renderItem={({ item }) => (
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  gap: horizontalScale(5),
                }}
              >
                <Image
                  source={{
                    uri: "https://images.unsplash.com/photo-1476480862126-209bfaa8edc8?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                  }}
                  style={{
                    height: 60,
                    width: 60,
                    borderRadius: 100,
                    borderWidth: 1,
                    borderColor: COLORS.whiteTail,
                  }}
                />
                <View
                  style={{
                    gap: verticalScale(5),
                    alignItems: "flex-start",
                  }}
                >
                  <CustomText fontFamily="medium">{item.name}</CustomText>
                  <View
                    style={{
                      backgroundColor: COLORS.nickel,
                      paddingVertical: verticalScale(4),
                      paddingHorizontal: horizontalScale(20),
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 100,
                    }}
                  >
                    <CustomText
                      color={COLORS.white}
                      fontFamily="medium"
                      fontSize={10}
                    >
                      {`${item.percentage}%`}
                    </CustomText>
                  </View>
                </View>
              </View>
            )}
            keyExtractor={(item, index) => item.name + index.toString()}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              gap: horizontalScale(10),
              paddingHorizontal: horizontalScale(5),
            }}
          />
        </View> */}
        <View>
          <FlatList
            horizontal
            contentContainerStyle={{
              gap: horizontalScale(5),
              marginVertical: verticalScale(20),
              paddingHorizontal: horizontalScale(10),
            }}
            data={setsTabData}
            renderItem={({ item }) => {
              return (
                <Pressable
                  key={item.value}
                  onPress={() => setSetsTab(item.value)}
                  style={[
                    styles.setsTabButton,
                    {
                      backgroundColor:
                        setsTab === item?.value
                          ? COLORS.whiteTail
                          : "transparent",
                    },
                  ]}
                >
                  <CustomText
                    fontSize={12}
                    fontFamily="semiBold"
                    color={
                      setsTab === item?.value ? COLORS.black : COLORS.whiteTail
                    }
                  >
                    {item?.label}
                  </CustomText>
                </Pressable>
              );
            }}
          />
        </View>

        <View style={{ flex: 1 }}>
          {historySets && historySets?.length > 0 && (
            <View style={{ width: wp(100), flexDirection: "row" }}>
              <View
                style={{
                  width: wp(10),
                  alignItems: "flex-start",
                }}
              />
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  paddingBottom: verticalScale(10),
                  width: wp(85),
                  justifyContent: "space-evenly",
                  marginBottom: verticalScale(5),
                }}
              >
                <CustomText
                  fontSize={14}
                  fontFamily="semiBold"
                  style={{
                    flex: 1,
                    textAlign: "center",
                  }}
                >
                  Reps
                </CustomText>
                <CustomText
                  fontSize={14}
                  fontFamily="semiBold"
                  style={{
                    flex: 1,
                    textAlign: "center",
                  }}
                >
                  Distance
                </CustomText>
                <CustomText
                  fontSize={14}
                  fontFamily="semiBold"
                  style={{
                    flex: 1,
                    textAlign: "center",
                  }}
                >
                  Weight(kg)
                </CustomText>
                <CustomText
                  fontSize={14}
                  fontFamily="semiBold"
                  style={{
                    flex: 1,
                    textAlign: "center",
                  }}
                >
                  Time
                </CustomText>
              </View>
            </View>
          )}
          <FlatList
            data={
              historySets
                ? prepareSetsForDisplay(historySets).map((set, index) => ({
                    Set: set.displayIndex,
                    Reps: set.reps,
                    Distance: set.distance || "0m", // Use actual distance field or default
                    Weight: set.weight,
                    Time: set.time,
                    difficulty:
                      set.difficulty ||
                      (index === 0
                        ? "Warmup"
                        : index === 1
                        ? "Easy"
                        : index === 2
                        ? "Medium"
                        : "Hard"), // Add default difficulty if not present
                    isNewlyAdded:
                      set.mainSetIndex !== undefined &&
                      set.mainSetIndex < newlyAddedSets.length, // Mark newly added sets
                    dropSets: set.dropSets || [], // Include drop sets data
                    originalSet: set, // Keep reference to original set data
                  }))
                : []
            }
            renderItem={({ item }) => {
              // Define colors based on difficulty
              const difficultyColors = {
                Warmup: "#6C757D", // Gray
                Easy: "#28A745", // Green
                Medium: "#FFC107", // Yellow
                Hard: "#DC3545", // Red
              };

              // Use difficulty color or fallback to index-based color
              const setColor =
                difficultyColors[
                  item.difficulty as keyof typeof difficultyColors
                ];

              // Determine if this is a drop set and style accordingly
              const isDropSet = item.isDropSet;
              const isMainSet = item.isMainSet;

              return (
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    width: wp(100),
                  }}
                >
                  <View
                    style={{
                      width: wp(10),
                      alignItems: "flex-start",
                      position: "relative",
                    }}
                  >
                    {/* Connection line for drop sets */}
                    {isDropSet && (
                      <View
                        style={{
                          position: "absolute",
                          left: horizontalScale(12),
                          top: -verticalScale(15),
                          width: 2,
                          height: verticalScale(30),
                          backgroundColor: setColor,
                          opacity: 0.6,
                        }}
                      />
                    )}

                    <View
                      style={{
                        backgroundColor: setColor,
                        borderTopEndRadius: 5,
                        borderBottomEndRadius: 5,
                        paddingHorizontal: horizontalScale(5),
                        paddingVertical: verticalScale(5),
                        width: horizontalScale(25),
                        borderLeftColor: setColor,
                      }}
                    >
                      <CustomText
                        style={{
                          textAlign: "center",
                        }}
                        fontSize={isDropSet ? 16 : 20} // Smaller font for drop sets
                        fontFamily="medium"
                      >
                        {item.Set}
                      </CustomText>
                    </View>
                  </View>
                  <View
                    style={{
                      width: wp(85),
                      flexDirection: "row",
                      gap: horizontalScale(10),
                      justifyContent: "space-evenly",
                      alignItems: "center",
                      borderTopWidth: isDropSet ? 0 : 0.5, // No top border for drop sets
                      borderTopColor: COLORS.whiteTail,
                      paddingVertical: verticalScale(7),
                      backgroundColor: item.isNewlyAdded
                        ? "rgba(255, 255, 255, 0.1)"
                        : isDropSet
                        ? "rgba(255, 255, 255, 0.05)" // Slightly different background for drop sets
                        : "transparent",
                      borderRadius: item.isNewlyAdded || isDropSet ? 5 : 0,
                      marginLeft: isDropSet ? horizontalScale(10) : 0, // Indent drop set data
                      borderLeftWidth: isDropSet ? 2 : 0,
                      borderLeftColor: isDropSet ? setColor : "transparent",
                    }}
                  >
                    <CustomText
                      fontSize={isDropSet ? 16 : 20} // Smaller font for drop sets
                      fontFamily="medium"
                      color={
                        item.isNewlyAdded
                          ? COLORS.white
                          : isDropSet
                          ? COLORS.whiteTail // Lighter color for drop sets
                          : COLORS.nickel
                      }
                      style={{
                        flex: 1,
                        textAlign: "center",
                      }}
                    >
                      {item.Reps}
                    </CustomText>
                    <CustomText
                      fontSize={isDropSet ? 16 : 20} // Smaller font for drop sets
                      fontFamily="medium"
                      color={
                        item.isNewlyAdded
                          ? COLORS.white
                          : isDropSet
                          ? COLORS.whiteTail // Lighter color for drop sets
                          : COLORS.nickel
                      }
                      style={{
                        flex: 1,
                        textAlign: "center",
                      }}
                    >
                      {item.Distance}
                    </CustomText>
                    <CustomText
                      fontSize={isDropSet ? 16 : 20} // Smaller font for drop sets
                      fontFamily="medium"
                      color={
                        item.isNewlyAdded
                          ? COLORS.white
                          : isDropSet
                          ? COLORS.whiteTail // Lighter color for drop sets
                          : COLORS.nickel
                      }
                      style={{
                        flex: 1,
                        textAlign: "center",
                      }}
                    >
                      {item.Weight}
                    </CustomText>
                    <CustomText
                      fontSize={isDropSet ? 16 : 20} // Smaller font for drop sets
                      fontFamily="medium"
                      color={
                        item.isNewlyAdded
                          ? COLORS.white
                          : isDropSet
                          ? COLORS.whiteTail // Lighter color for drop sets
                          : COLORS.nickel
                      }
                      style={{
                        flex: 1,
                        textAlign: "center",
                      }}
                    >
                      {item.Time}
                    </CustomText>
                  </View>
                </View>
              );
            }}
            ListEmptyComponent={({}) => {
              return (
                <View style={styles.noHistoryContainer}>
                  <CustomText
                    fontSize={16}
                    fontFamily="medium"
                    color={COLORS.whiteTail}
                    style={{ textAlign: "center" }}
                  >
                    No history found.{"\n"} Click "Add" to create a new set.
                  </CustomText>
                </View>
              );
            }}
            ListFooterComponent={() => {
              return (
                <View
                  style={{
                    width: wp(100),
                    paddingHorizontal: horizontalScale(10),
                    gap: verticalScale(15),
                  }}
                >
                  {/* Add buttons */}
                  <View style={styles.addButtonsContainer}>
                    <PrimaryButton
                      title="Add"
                      onPress={() => {
                        setShowAddSetUi(true);
                      }}
                      isFullWidth={false}
                      style={{
                        alignSelf: "flex-end",
                        paddingVertical: verticalScale(3),
                        paddingHorizontal: horizontalScale(22),
                        borderRadius: verticalScale(5),
                      }}
                    />
                  </View>
                </View>
              );
            }}
          />
        </View>
      </View>
    );
  };

  const renderMainView = () => {
    switch (activeTab) {
      case 1:
        return renderSets();
      case 2:
        return renderDetailsTab();
      case 3:
        return renderHistory();
    }
  };

  return (
    <View
      style={{
        flex: 1,
        paddingVertical: verticalScale(10),
        gap: verticalScale(20),
      }}
    >
      <CustomText
        style={{ paddingHorizontal: horizontalScale(12) }}
        fontFamily="medium"
      >
        {exerciseData.name +
          " " +
          exerciseData.sets +
          "x" +
          exerciseData.reps.split("-")[0]}
      </CustomText>
      {!showAddSetUi && renderTabs()}
      {renderMainView()}
    </View>
  );
};

export default ExerciseDetails;

const styles = StyleSheet.create({
  tabContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
  },
  tabButton: {
    justifyContent: "center",
    paddingHorizontal: horizontalScale(30),
    paddingVertical: verticalScale(5),
    borderRadius: 10,
  },
  setsTabButton: {
    justifyContent: "center",
    paddingVertical: verticalScale(4),
    paddingHorizontal: horizontalScale(10),
    borderRadius: 100,
  },
  tagContainer: {
    flexDirection: "row",
    gap: horizontalScale(8),
    alignItems: "center",
    justifyContent: "center",
    marginBottom: verticalScale(10),
  },
  tag: {
    backgroundColor: COLORS.brown,
    paddingVertical: verticalScale(5),
    paddingHorizontal: horizontalScale(8),
    borderRadius: 5,
    borderWidth: 1,
    borderColor: COLORS.brown,
  },
  noHistoryContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: verticalScale(50),
    paddingHorizontal: horizontalScale(20),
  },
  // New styles for the sets table
  setsHeaderRow: {
    flexDirection: "row",
    paddingVertical: verticalScale(10),
    paddingHorizontal: horizontalScale(10),
    alignItems: "center",
  },
  headerText: {
    flex: 1,
    textAlign: "center",
  },
  divider: {
    height: 1,
    backgroundColor: "#333333",
    width: "100%",
  },
  setRow: {
    flexDirection: "row",
    paddingVertical: verticalScale(10),
    paddingHorizontal: horizontalScale(10),
    alignItems: "center",
  },
  setNumberContainer: {
    width: 40,
    alignItems: "center",
    justifyContent: "center",
  },
  setNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    textAlign: "center",
    textAlignVertical: "center",
    overflow: "hidden",
    lineHeight: 30,
  },
  setCellText: {
    flex: 1,
    textAlign: "center",
  },
  // Difficulty selector styles
  difficultyContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: verticalScale(10),
    paddingHorizontal: horizontalScale(20),
  },
  difficultyButton: {
    paddingVertical: verticalScale(4),
    paddingHorizontal: horizontalScale(15),
    borderRadius: 20,
    borderWidth: 1,
    alignItems: "center",
    justifyContent: "center",
    minWidth: horizontalScale(70),
  },
  // Add buttons styles
  addButtonsContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginVertical: verticalScale(10),
  },
  addDropSetButton: {
    backgroundColor: "#007BFF",
    paddingVertical: verticalScale(10),
    paddingHorizontal: horizontalScale(15),
    borderRadius: 5,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
    marginRight: horizontalScale(10),
  },
  addSetButton: {
    backgroundColor: "#FF9500",
    paddingVertical: verticalScale(10),
    paddingHorizontal: horizontalScale(15),
    borderRadius: 5,
    alignItems: "center",
    justifyContent: "center",
    flex: 1,
  },
});
