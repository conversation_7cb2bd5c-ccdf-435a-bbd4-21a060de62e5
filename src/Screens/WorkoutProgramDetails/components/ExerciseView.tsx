import React, { FC, useMemo } from "react";
import {
  Animated,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";
import ICONS from "../../../Assets/Icons";
import CustomIcon from "../../../Components/CustomIcon";
import { CustomText } from "../../../Components/CustomText";
import COLORS from "../../../Utilities/Colors";
import { horizontalScale, verticalScale, wp } from "../../../Utilities/Metrics";
import { Exercise, WeeklyStructure } from "../../../Seeds/TrainingPLans";
import PrimaryButton from "../../../Components/PrimaryButton";
import { useNavigation } from "@react-navigation/native";

// Define a type for a Superset
type Superset = {
  type: "superset";
  exercises: Exercise[];
};

// Union type to allow both individual exercises and supersets in the list
type ExerciseListItem = Exercise | Superset;

// Type for muscle data with percentage
export type MuscleData = {
  name: string;
  percentage: number;
};

// Update the props to include the states and handlers passed from parent
type ExerciseData = {
  data: WeeklyStructure[];
  isSupersetSelected: boolean;
  onPressSuperset: () => void;
  selectedExercises: string[];
  exerciseData: ExerciseListItem[];
  setExerciseData: React.Dispatch<React.SetStateAction<ExerciseListItem[]>>;
  handleExercisePress: (title: string) => void;
  handleLongExercisePress: (item: any) => void;
  handleDeleteSelected: () => void;
  handleClickSuperSet: () => void;
  fadeAnim: Animated.Value;
};

const ExerciseView: FC<ExerciseData> = ({
  data,
  isSupersetSelected,
  onPressSuperset,
  selectedExercises,
  exerciseData,
  setExerciseData,
  handleExercisePress,
  handleLongExercisePress,
  handleDeleteSelected,
  handleClickSuperSet,
}) => {
  const navigation = useNavigation<any>();

  // Calculate targeted muscles and their percentages based on superset or all exercises
  const muscleData = useMemo(() => {
    const muscleCount: { [key: string]: number } = {};
    let totalMuscleMentions = 0;

    if (isSupersetSelected) {
      const superset = exerciseData.find(
        (item) => "type" in item && item.type === "superset"
      ) as Superset | undefined;
      if (superset) {
        superset.exercises.forEach((exercise) => {
          exercise.targetMuscles?.forEach((muscle) => {
            muscleCount[muscle] = (muscleCount[muscle] || 0) + 1;
            totalMuscleMentions++;
          });
        });
      }
    } else {
      exerciseData.forEach((item: any) => {
        if ("type" in item && item.type === "superset") {
          item.exercises.forEach((exercise: any) => {
            exercise.targetMuscles?.forEach((muscle: any) => {
              muscleCount[muscle] = (muscleCount[muscle] || 0) + 1;
              totalMuscleMentions++;
            });
          });
        } else {
          item.targetMuscles?.forEach((muscle: any) => {
            muscleCount[muscle] = (muscleCount[muscle] || 0) + 1;
            totalMuscleMentions++;
          });
        }
      });
    }

    const muscles: MuscleData[] = Object.keys(muscleCount).map((muscle) => ({
      name: muscle,
      percentage: totalMuscleMentions
        ? Math.round((muscleCount[muscle] / totalMuscleMentions) * 100)
        : 0,
    }));

    return muscles.sort((a, b) => b.percentage - a.percentage);
  }, [exerciseData, isSupersetSelected]);

  const renderExerciseList = () => {
    return (
      <FlatList
        data={exerciseData}
        style={{}}
        contentContainerStyle={{
          gap: verticalScale(10),
        }}
        renderItem={({ item }: any) => {
          if ("type" in item && item.type === "superset") {
            return (
              <TouchableOpacity
                onPress={onPressSuperset}
                activeOpacity={1}
                style={{
                  padding: verticalScale(4),
                  gap: verticalScale(5),
                  borderColor: COLORS.whiteTail,
                  borderWidth: 1,
                  borderRadius: 10,
                  width: wp(95),
                }}
              >
                <View
                  style={{
                    width: "100%",
                    backgroundColor: COLORS.brown,
                    paddingHorizontal: horizontalScale(10),
                    paddingVertical: verticalScale(2),
                    borderTopRightRadius: 10,
                    borderTopLeftRadius: 10,
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <CustomText fontFamily="italic" fontSize={14}>
                    SUPERSET
                  </CustomText>
                  <View
                    style={{
                      flexDirection: "row",
                      gap: horizontalScale(5),
                      alignItems: "center",
                    }}
                  >
                    <CustomIcon
                      Icon={ICONS.ThreeLineSideDotMenuView}
                      height={verticalScale(15)}
                    />
                  </View>
                </View>
                <View
                  style={{
                    width: "98%",
                    gap: verticalScale(5),
                    alignSelf: "center",
                  }}
                >
                  {item.exercises.map((exercise: any, index: number) => {
                    const isSelected = selectedExercises.includes(
                      exercise.name
                    );
                    return (
                      <View
                        key={index}
                        style={{
                          flexDirection: "row",
                          justifyContent: "space-between",
                          borderRadius: verticalScale(10),
                          backgroundColor: COLORS.lightBrown,
                          padding: verticalScale(5),
                        }}
                      >
                        <Image
                          source={{
                            uri:
                              exercise.image ??
                              "https://images.unsplash.com/photo-1476480862126-209bfaa8edc8?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                          }}
                          style={styles.ExerciseImage}
                        />
                        <View style={styles.ExerciseDetails}>
                          <CustomText
                            color={COLORS.yellow}
                            fontFamily="medium"
                            fontSize={12}
                          >
                            {exercise.name}
                          </CustomText>
                          <CustomText
                            color={COLORS.white}
                            fontFamily="medium"
                            fontSize={12}
                          >
                            {`${exercise.sets} sets x ${exercise.reps} reps`}
                          </CustomText>
                        </View>
                        <View style={{ justifyContent: "center" }}>
                          <CustomIcon
                            Icon={ICONS.SidMultiDotView}
                            height={verticalScale(27)}
                          />
                        </View>
                      </View>
                    );
                  })}
                </View>
              </TouchableOpacity>
            );
          }

          const isSelected = selectedExercises.includes(item.name);

          return (
            <TouchableOpacity
              onLongPress={() => handleLongExercisePress(item.name)}
              onPress={() => handleExercisePress(item)}
              delayLongPress={400}
              activeOpacity={0.7}
              style={[
                styles.ExerciseItem,
                isSelected && styles.selectedExerciseItem,
              ]}
            >
              <Image
                source={{
                  uri:
                    item.image ??
                    "https://images.unsplash.com/photo-1476480862126-209bfaa8edc8?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                }}
                style={styles.ExerciseImage}
              />
              <View style={styles.ExerciseDetails}>
                <CustomText
                  color={COLORS.yellow}
                  fontFamily="medium"
                  fontSize={12}
                >
                  {item.name}
                </CustomText>
                <CustomText
                  color={COLORS.white}
                  fontFamily="medium"
                  fontSize={12}
                >
                  {`${item.sets} sets x ${item.reps} reps`}
                </CustomText>
              </View>
              <View style={{ justifyContent: "center" }}>
                <CustomIcon
                  Icon={ICONS.SidMultiDotView}
                  height={verticalScale(27)}
                />
              </View>
            </TouchableOpacity>
          );
        }}
        keyExtractor={(item: any, index) => item.type + index.toString()}
        ListFooterComponent={() => (
          <View style={{ alignItems: "flex-end" }}>
            <PrimaryButton
              onPress={() => {
                navigation.navigate("exerciseList");
              }}
              isFullWidth={false}
              style={{
                width: "auto",
                paddingVertical: verticalScale(8),
                paddingHorizontal: horizontalScale(12),
                borderRadius: verticalScale(5),
              }}
              textSize={10}
              title="Add Exercise"
            />
          </View>
        )}
      />
    );
  };

  const renderSupersetDetails = () => {
    const superset = exerciseData.find(
      (item) => "type" in item && item.type === "superset"
    ) as Superset | undefined;

    if (!superset) return null;

    const warmUpTimeSeconds = 180; // 3 minutes static warm-up
    const coolDownTimeSeconds = 180; // 3 minutes static cool-down
    let workingTimeSeconds = 0;

    const timePerRep = 3; // 3 seconds per rep (controlled lifting)

    superset.exercises.forEach((exercise) => {
      const repsRange = exercise.reps.split("-");
      const reps =
        repsRange.length > 1
          ? Math.round((parseInt(repsRange[0]) + parseInt(repsRange[1])) / 2)
          : parseInt(repsRange[0]) || 0;

      const restSeconds = parseInt(exercise.rest) || 0;

      const exerciseTime =
        exercise.sets * reps * timePerRep + restSeconds * (exercise.sets - 1);

      workingTimeSeconds += exerciseTime;
    });

    const fullCompletionTimeSeconds =
      warmUpTimeSeconds + workingTimeSeconds + coolDownTimeSeconds;

    const formatTime = (seconds: number): string => {
      const mins = Math.floor(seconds / 60);
      const secs = seconds % 60;
      return `${mins.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    };

    return (
      <View
        style={{
          gap: verticalScale(10),
          width: wp(95),
          flex: 1,
        }}
      >
        <View
          style={{
            padding: verticalScale(4),
            gap: verticalScale(20),
            borderRadius: 10,
          }}
        >
          <CustomText fontFamily="bold" fontSize={14}>
            Superset
          </CustomText>
          <View
            style={{
              width: "98%",
              gap: verticalScale(5),
              alignSelf: "center",
            }}
          >
            {superset.exercises.map((exercise, index) => (
              <View
                key={index}
                style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  borderRadius: verticalScale(10),
                  backgroundColor: COLORS.lightBrown,
                  padding: verticalScale(5),
                  borderWidth: 1,
                  borderColor: COLORS.white,
                }}
              >
                <Image
                  source={{
                    uri:
                      exercise.image ??
                      "https://images.unsplash.com/photo-1476480862126-209bfaa8edc8?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                  }}
                  style={styles.ExerciseImage}
                />
                <View style={styles.ExerciseDetails}>
                  <CustomText
                    color={COLORS.yellow}
                    fontFamily="medium"
                    fontSize={12}
                  >
                    {exercise.name}
                  </CustomText>
                  <CustomText
                    color={COLORS.white}
                    fontFamily="medium"
                    fontSize={12}
                  >
                    {`${exercise.sets} sets x ${exercise.reps} reps`}
                  </CustomText>
                </View>
                <View style={{ justifyContent: "center" }}>
                  <CustomIcon
                    Icon={ICONS.SidMultiDotView}
                    height={verticalScale(27)}
                  />
                </View>
              </View>
            ))}
          </View>
          <CustomText fontFamily="bold" fontSize={14}>
            Rest Time
          </CustomText>
          <View style={{ gap: verticalScale(10) }}>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <CustomText fontSize={14}>Warm-up Time</CustomText>
              <CustomText fontSize={14}>
                {formatTime(warmUpTimeSeconds)}
              </CustomText>
            </View>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <CustomText fontSize={14}>Working Time</CustomText>
              <CustomText fontSize={14}>
                {formatTime(workingTimeSeconds)}
              </CustomText>
            </View>
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
              }}
            >
              <CustomText fontSize={14}>Full Completion Time</CustomText>
              <CustomText fontSize={14}>
                {formatTime(fullCompletionTimeSeconds)}
              </CustomText>
            </View>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View
      style={{
        gap: verticalScale(10),
        alignItems: "center",
        flex: 1,
      }}
    >
      {isSupersetSelected ? (
        <ScrollView
          style={{
            width: wp(100),
            paddingHorizontal: horizontalScale(10),
            flex: 1,
          }}
        >
          <FlatList
            horizontal
            data={muscleData}
            renderItem={({ item, index }) => (
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  gap: horizontalScale(5),
                  borderRadius: verticalScale(10),
                  padding: verticalScale(5),
                }}
              >
                <Image
                  source={{
                    uri: "https://images.unsplash.com/photo-1476480862126-209bfaa8edc8?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                  }}
                  style={{
                    height: 60,
                    width: 60,
                    borderRadius: 100,
                    borderWidth: 1,
                    borderColor: COLORS.whiteTail,
                  }}
                />
                <View
                  style={{
                    gap: verticalScale(5),
                    alignItems: "flex-start",
                  }}
                >
                  <CustomText fontFamily="medium">{item.name}</CustomText>
                  <View
                    style={{
                      backgroundColor: COLORS.nickel,
                      paddingVertical: verticalScale(4),
                      paddingHorizontal: horizontalScale(20),
                      justifyContent: "center",
                      alignItems: "center",
                      borderRadius: 100,
                    }}
                  >
                    <CustomText
                      color={COLORS.white}
                      fontFamily="medium"
                      fontSize={10}
                    >
                      {`${item.percentage}%`}
                    </CustomText>
                  </View>
                </View>
              </View>
            )}
            keyExtractor={(item, index) => item.name + index.toString()}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              gap: horizontalScale(10),
              paddingHorizontal: horizontalScale(5),
              marginVertical: verticalScale(20),
            }}
          />
          <View style={{ flex: 1 }}>{renderSupersetDetails()}</View>
        </ScrollView>
      ) : (
        <>
          {selectedExercises.length > 0 ? (
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "space-between",
                width: wp(100),
                paddingHorizontal: horizontalScale(10),
                paddingBottom: verticalScale(10),
              }}
            >
              <View style={{ flexDirection: "row", gap: horizontalScale(20) }}>
                <TouchableOpacity
                  onPress={handleDeleteSelected}
                  style={styles.actionButton}
                >
                  <CustomIcon Icon={ICONS.DeleteIcon} height={15} width={15} />
                  <CustomText fontSize={6} fontFamily="bold">
                    DELETE
                  </CustomText>
                </TouchableOpacity>
                <TouchableOpacity style={styles.actionButton}>
                  <CustomIcon Icon={ICONS.CopyIcon} height={15} width={15} />
                  <CustomText fontSize={6} fontFamily="bold">
                    COPY
                  </CustomText>
                </TouchableOpacity>
              </View>
              {selectedExercises.length > 1 && (
                <TouchableOpacity
                  onPress={handleClickSuperSet}
                  style={styles.actionButton}
                >
                  <CustomIcon
                    Icon={ICONS.SuperSetIcon}
                    height={15}
                    width={15}
                  />
                  <CustomText fontSize={6} fontFamily="bold">
                    SUPERSET
                  </CustomText>
                </TouchableOpacity>
              )}
            </View>
          ) : (
            <View
              style={{ width: wp(100), paddingHorizontal: horizontalScale(10) }}
            >
              <FlatList
                horizontal
                data={muscleData}
                renderItem={({ item, index }) => (
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      gap: horizontalScale(5),
                    }}
                  >
                    <Image
                      source={{
                        uri: "https://images.unsplash.com/photo-1476480862126-209bfaa8edc8?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
                      }}
                      style={{
                        height: 60,
                        width: 60,
                        borderRadius: 100,
                        borderWidth: 1,
                        borderColor: COLORS.whiteTail,
                      }}
                    />
                    <View
                      style={{
                        gap: verticalScale(5),
                        alignItems: "flex-start",
                      }}
                    >
                      <CustomText fontFamily="medium">{item.name}</CustomText>
                      <View
                        style={{
                          backgroundColor: COLORS.nickel,
                          paddingVertical: verticalScale(4),
                          paddingHorizontal: horizontalScale(20),
                          justifyContent: "center",
                          alignItems: "center",
                          borderRadius: 100,
                        }}
                      >
                        <CustomText
                          color={COLORS.white}
                          fontFamily="medium"
                          fontSize={10}
                        >
                          {`${item.percentage}%`}
                        </CustomText>
                      </View>
                    </View>
                  </View>
                )}
                keyExtractor={(item, index) => item.name + index.toString()}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                  gap: horizontalScale(10),
                  paddingHorizontal: horizontalScale(5),
                }}
              />
            </View>
          )}
          {renderExerciseList()}
        </>
      )}
    </View>
  );
};

export default ExerciseView;

const styles = StyleSheet.create({
  ExerciseItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderWidth: 1,
    borderRadius: verticalScale(10),
    borderColor: COLORS.whiteTail,
    backgroundColor: COLORS.lightBrown,
    width: wp(95),
    padding: verticalScale(5),
  },
  selectedExerciseItem: {
    backgroundColor: COLORS.skinColor,
  },
  ExerciseImage: {
    height: "100%",
    minHeight: 71,
    width: 66,
    borderRadius: 10,
    resizeMode: "cover",
  },
  ExerciseDetails: {
    paddingHorizontal: horizontalScale(10),
    justifyContent: "flex-start",
    gap: verticalScale(5),
    paddingVertical: verticalScale(4),
    flex: 1,
  },
  TargetMusclesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: horizontalScale(5),
  },
  TargetMuscleItem: {
    backgroundColor: COLORS.brown,
    borderRadius: 5,
    paddingHorizontal: horizontalScale(8),
    paddingVertical: verticalScale(2),
  },
  actionButton: {
    alignItems: "center",
    borderWidth: 1,
    borderColor: COLORS.whiteTail,
    borderRadius: 100,
    justifyContent: "center",
    height: 40,
    width: 40,
  },
});
