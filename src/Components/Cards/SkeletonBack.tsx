import React, { <PERSON><PERSON><PERSON>, <PERSON>, SetStateAction } from "react";
import { StyleSheet, View } from "react-native";
import Svg, { Circle, G, Line, Path, Rect, Text } from "react-native-svg";
import { hp, responsiveFontSize, wp } from "../../Utilities/Metrics";
import COLORS from "../../Utilities/Colors";

type SkeletonBackProps = {
  showLabel?: boolean;
  width?: number;
  height?: number;
  containerWidth?: number;
  selectedMuscles?: string[];
  setSelectedMuscles?: Dispatch<SetStateAction<any>>;
};

const SkeletonBack: FC<SkeletonBackProps> = ({
  showLabel = true,
  width = wp(80),
  height = hp(60),
  containerWidth,
  selectedMuscles,
  setSelectedMuscles,
}) => {
  // SVG dimensions and scaling
  const svgWidth = width;
  const svgHeight = height;

  return (
    <View
      style={[
        styles.container,
        {
          width: containerWidth ? containerWidth : "100%",
        },
      ]}
    >
      <Svg
        width={svgWidth}
        height={svgHeight}
        viewBox="0 30 369 260"
        fill="none"
      >
        <G>
          <Path
            d="M212.463 436.416C210.125 436.416 208.146 436.028 206.617 435.253L205.718 434.769L205.268 433.799C202.57 427.5 202.93 423.139 203.469 417.808C203.199 415.482 202.84 412.768 202.57 410.055V409.958C202.3 407.729 202.12 405.403 202.3 402.883L203.379 395.808C203.379 395.614 203.469 395.42 203.469 395.226C203.559 394.839 203.649 394.354 203.739 393.87C203.919 393.288 204.099 392.804 204.279 392.222C204.459 391.544 204.638 390.962 204.818 390.284C205.358 388.152 205.538 384.372 204.998 381.949C204.459 379.332 204.099 377.103 203.739 374.971C203.289 372.548 202.93 370.222 202.3 367.411L201.041 361.306C200.771 360.821 200.501 360.53 200.411 360.24C198.702 357.041 197.713 354.037 197.443 351.032C196.903 345.702 197.443 340.469 198.972 335.041C199.332 333.781 199.692 332.618 199.961 331.552C200.771 329.032 201.311 327.191 201.58 324.768L198.522 320.601C195.464 313.041 194.385 308.098 193.126 302.38C192.766 300.733 192.406 299.085 191.957 297.244C190.068 289.49 188.719 281.155 188.449 275.922C188.179 271.173 188.089 265.455 187.909 259.931C187.729 254.213 187.639 248.301 187.37 243.455C187.28 242.583 187.549 238.803 188.089 230.274C188.269 227.464 188.449 224.265 188.449 223.781V220.679C188.449 220.389 188.449 220.001 188.449 219.71C188.539 218.838 188.899 218.063 189.348 217.384C188.359 216.318 187.549 215.155 186.83 213.992C186.11 215.155 185.211 216.318 184.312 217.384C184.851 217.966 185.121 218.838 185.211 219.71C185.211 220.098 185.211 220.389 185.211 220.679V223.781C185.211 224.168 185.391 227.367 185.571 230.177C186.11 238.706 186.38 242.486 186.29 243.358C186.02 248.204 185.841 254.116 185.751 259.834C185.571 265.358 185.481 271.076 185.211 275.825C184.941 281.059 183.592 289.393 181.703 297.147C181.253 298.988 180.894 300.636 180.534 302.283C179.275 308.001 178.106 312.847 175.137 320.504L172.079 324.671C172.349 327.094 172.889 328.839 173.698 331.455C174.058 332.521 174.328 333.587 174.688 334.944C176.217 340.372 176.756 345.605 176.217 350.936C175.947 353.94 174.958 357.041 173.249 360.143C173.159 360.433 172.889 360.724 172.619 361.209L171.36 367.314C170.82 370.125 170.37 372.451 169.921 374.874C169.561 377.006 169.201 379.235 168.662 381.852C168.212 384.178 168.302 387.958 168.841 390.187C169.021 390.865 169.201 391.447 169.381 392.125C169.561 392.707 169.741 393.191 169.921 393.773C170.101 394.257 170.101 394.645 170.191 395.13C170.191 395.323 170.281 395.517 170.281 395.711L171.36 402.786C171.63 405.306 171.36 407.729 171.09 409.861V409.958C170.82 412.671 170.46 415.385 170.191 417.711C170.82 423.139 171.18 427.403 168.392 433.702L167.942 434.672L167.043 435.156C165.514 435.932 163.535 436.319 161.196 436.319C158.048 436.319 152.742 435.447 150.673 433.024C150.134 432.346 149.684 431.667 149.234 431.086C148.784 430.504 148.425 429.923 148.065 429.438C146.986 428.275 145.367 427.79 143.478 427.209C140.959 426.434 138.171 425.658 135.923 422.945C133.584 420.134 132.955 416.548 134.124 413.253C135.383 409.764 138.621 406.76 142.129 405.79C142.938 405.597 144.107 405.5 145.726 405.5C147.705 405.5 149.954 405.694 151.573 405.79L151.483 405.112C151.483 405.015 151.483 404.918 151.483 404.918C151.393 404.53 151.393 403.949 151.393 403.368C151.483 402.883 151.483 402.301 151.573 401.817C151.752 400.169 151.932 398.425 152.202 396.68C152.742 393.579 152.652 390.187 151.752 386.213C149.864 377.588 147.975 368.671 145.367 360.046C145.277 359.561 145.097 359.173 144.917 358.689C144.017 355.878 143.028 352.583 143.028 349.094V347.64C142.668 346.671 142.578 345.702 142.488 344.927C142.309 342.988 142.309 338.53 142.488 336.107C142.758 333.297 143.568 330.486 144.377 327.676C144.647 326.803 144.917 325.931 145.097 324.962C146.626 319.341 147.795 316.627 149.504 312.944C149.594 312.653 149.774 312.363 149.864 312.072L149.774 311.684C149.684 311.49 149.594 311.2 149.504 311.006C149.234 310.327 148.874 309.455 148.784 308.389L148.694 307.905C148.425 305.772 148.155 303.64 148.245 301.411C148.335 299.279 148.425 297.244 148.605 295.208C148.784 292.882 148.874 290.653 148.964 288.424V288.133L147.255 277.86L147.165 277.376C146.986 276.697 146.896 275.922 146.806 275.147C146.446 272.724 146.086 270.204 145.726 267.781C144.827 261.772 143.928 255.473 143.388 249.27C142.758 242.389 142.578 235.411 142.399 228.724V227.851C142.309 223.781 142.399 219.323 142.758 213.798C143.028 209.825 143.568 205.851 144.017 202.459C144.377 200.23 145.457 198.679 147.075 198.001C146.986 197.129 146.716 191.798 146.716 191.798L146.626 191.604C146.446 191.12 146.176 190.248 146.356 189.085L146.626 187.825C147.165 184.82 147.705 181.719 148.425 178.714C148.964 176.292 149.684 173.772 150.853 171.446C150.134 170.477 149.684 169.217 149.954 167.957C150.673 164.274 151.303 160.979 152.022 157.877C152.292 156.521 153.281 155.454 154.451 155.067C153.641 153.128 153.012 151.287 152.382 149.543C150.943 144.987 150.313 140.432 149.684 135.877C149.504 134.811 149.324 130.062 149.324 130.062C149.324 130.062 147.435 134.424 146.896 135.587C145.457 138.494 143.838 141.595 142.219 144.503C141.679 145.569 140.869 146.441 140.06 147.217C140.24 147.798 140.42 148.38 140.42 148.961C140.42 149.252 140.42 149.639 140.42 150.027C140.42 151.287 140.42 152.741 139.97 154.195C139.43 155.842 138.801 157.393 138.081 158.847C137.901 159.331 137.632 159.816 137.452 160.203C137.182 160.688 136.912 161.269 136.642 161.754C136.282 162.432 135.923 163.111 135.743 163.692C133.584 170.767 130.796 178.036 127.468 185.014C125.31 189.472 123.151 194.027 120.992 198.486C119.643 201.199 118.294 203.913 117.035 206.627C116.405 207.886 115.506 208.565 114.786 208.759C114.786 210.697 114.786 212.635 114.696 214.477V216.221C114.966 218.935 114.067 221.358 113.347 223.296C113.257 223.587 113.077 223.975 112.987 224.265C112.268 226.301 111.368 228.433 110.019 230.371C109.929 230.565 109.839 230.759 109.75 230.953L109.03 232.309C108.31 233.763 107.591 235.217 106.871 236.671C105.972 238.415 105.073 239.772 104.173 241.032C103.274 242.195 101.835 242.68 100.755 242.68C100.486 242.68 100.216 242.68 100.036 242.583C98.8666 245.006 97.1577 246.362 94.999 246.459C94.9091 246.459 94.8192 246.459 94.7292 246.459C93.6499 246.459 92.8404 246.072 92.3008 245.684C92.2108 245.684 92.2108 245.781 92.1209 245.781C91.5812 246.169 90.8617 246.362 90.0522 246.362C89.5126 246.362 88.1634 246.265 87.0841 245.199C86.0048 244.133 85.825 242.583 85.6451 241.71C85.5551 241.032 85.6451 240.45 85.735 239.966C84.9255 239.384 84.206 237.931 84.1161 237.834L83.7563 235.023C83.8462 234.635 84.0261 234.248 84.1161 233.957C84.3859 233.085 84.6557 232.213 85.0155 231.34C86.0048 229.305 86.9942 227.27 87.9836 225.235L88.4333 224.265C87.6238 224.459 85.4652 224.944 85.4652 224.944C85.0155 225.041 84.4758 225.138 84.0261 225.138C82.0474 225.138 80.2485 224.168 79.0793 222.424C78.7195 221.939 77.7302 220.583 78.1799 218.741C78.6296 216.997 80.0687 216.318 80.6083 216.027C83.1267 214.864 85.4652 213.701 86.8143 211.666C87.354 210.891 87.8936 210.212 88.3433 209.534L88.793 208.953C90.5019 206.627 93.0203 203.816 96.7079 202.168C97.1577 201.975 97.5174 201.878 97.8772 201.781C97.9671 201.393 98.0571 201.005 98.147 200.618C98.6867 199.067 99.2263 197.613 99.676 196.063C101.115 191.798 102.644 187.437 103.993 183.076C104.533 181.428 104.983 179.684 105.432 177.842C105.702 176.776 106.961 173.287 107.231 172.899L109.3 169.798L110.019 162.723C110.199 153.807 114.067 146.732 118.834 138.979L120.363 137.622C120.453 133.357 121.172 129.578 122.611 126.089C122.791 125.701 122.971 125.313 123.151 125.023C122.971 124.635 122.791 124.247 122.701 123.763C122.521 122.987 122.251 121.921 122.431 120.855C122.521 120.371 122.611 119.886 122.611 119.401C122.881 117.657 123.061 115.719 123.691 113.877C124.32 111.648 125.22 109.613 126.029 107.578C126.209 106.996 126.479 106.512 126.659 105.93C127.018 104.961 127.558 104.379 128.098 103.895C128.098 103.604 128.098 103.313 128.098 103.023C128.188 102.344 128.188 101.666 128.278 101.084C128.457 99.5335 128.547 97.9828 128.817 96.4322C129.807 90.4233 131.695 86.0621 134.663 82.5731C138.531 78.1149 143.478 74.9166 149.684 72.5906C150.403 72.2999 152.472 71.8153 152.472 71.8153L154.631 71.2338C155.62 70.9431 156.519 70.7492 157.509 70.4585C158.498 70.1677 159.487 69.877 160.477 69.5862L164.794 67.0664C164.974 66.9695 165.154 66.8726 165.334 66.7756L166.773 65.8065C168.032 65.0311 169.291 64.1589 170.46 63.3836C171.18 62.899 171.989 62.0267 172.259 59.7007C172.349 58.3439 172.529 56.987 172.619 55.5333C171.45 54.758 169.921 53.4011 169.651 50.8813C169.561 49.9121 169.471 49.0399 169.381 48.0707C169.291 47.0046 169.201 45.9385 169.111 44.8725L169.021 44.0002C168.122 43.8064 167.133 43.3218 166.503 42.3526C165.873 41.3835 165.424 40.3174 164.974 39.4451L164.884 39.2513C164.164 37.7006 163.535 36.1499 162.905 34.5024L162.815 34.2116C162.186 32.6609 162.366 30.6257 163.445 28.8812C164.254 27.4274 165.424 26.5552 166.683 26.2644C166.683 25.3922 166.683 24.5199 166.683 23.6477C166.953 16.0882 169.201 10.1763 173.518 5.815C176.487 2.81059 180.264 0.969167 184.581 0.387667C185.391 0.29075 186.2 0.193833 187.01 0.193833C196.454 0.193833 204.459 7.26876 206.527 17.445C207.067 20.2556 207.337 23.1631 207.247 26.3614L207.427 26.4583C209.675 27.1367 211.744 30.1411 211.654 32.3702V32.8548L211.564 33.2424C211.474 33.5332 211.384 33.9209 211.204 34.3085C210.215 36.5376 209.225 38.8636 208.236 41.0927C208.056 41.5773 207.607 42.5465 206.707 43.128C206.167 43.4187 205.628 43.6125 205.268 43.8064C205.178 43.8064 205.088 43.9033 205.088 43.9033L204.908 45.5509C204.818 46.3262 204.728 47.1015 204.638 47.8769C204.548 49.0399 204.369 50.2998 204.189 51.4628C203.829 53.7888 202.21 55.0487 201.041 55.7271C201.131 56.7932 201.221 57.8593 201.221 58.9254C201.311 60.9606 202.03 62.3175 203.469 63.2866C204.728 64.1589 205.988 64.9342 207.247 65.7096C207.786 66.0972 208.416 66.4849 208.956 66.7756L212.823 69.2955C214.172 69.6831 215.521 70.0708 216.781 70.4585C217.5 70.6523 218.31 70.9431 219.029 71.1369L221.278 71.8153C221.278 71.8153 222.717 72.203 223.346 72.3968C229.552 74.6259 234.499 77.9211 238.367 82.3792C241.335 85.8682 243.224 90.2295 244.213 96.2383C244.483 97.789 244.573 99.3397 244.753 100.89C244.843 101.569 245.022 103.507 244.932 103.798C245.472 104.186 245.922 104.864 246.372 105.833C246.551 106.415 246.821 106.899 247.001 107.481C247.811 109.516 248.71 111.551 249.34 113.78C249.879 115.622 250.149 117.463 250.419 119.305C250.509 119.789 250.509 120.274 250.599 120.758C250.779 121.824 250.599 122.89 250.329 123.666C250.239 124.15 250.059 124.538 249.879 124.926C250.059 125.216 250.239 125.604 250.419 125.992C251.858 129.481 252.578 133.261 252.667 137.525L254.196 138.882C258.873 146.635 262.831 153.71 263.011 162.626V164.468L263.64 169.798C263.64 169.798 267.238 176.776 267.508 177.842C267.958 179.684 268.407 181.428 268.947 183.076C270.386 187.437 271.825 191.798 273.264 196.063C273.804 197.613 274.344 199.067 274.793 200.618C274.883 201.005 275.063 201.393 275.063 201.781C275.423 201.878 275.783 201.975 276.232 202.168C280.01 203.816 282.438 206.723 284.147 208.953L284.597 209.631C285.137 210.309 285.676 210.988 286.126 211.763C287.565 213.895 289.903 214.961 292.332 216.124C292.872 216.415 294.311 217.094 294.76 218.838C295.21 220.583 294.221 222.036 293.861 222.521C292.602 224.265 290.893 225.235 288.914 225.235C288.464 225.235 287.925 225.138 287.475 225.041L287.025 224.944C286.216 224.75 285.406 224.556 284.507 224.362L285.047 225.428C286.036 227.367 287.025 229.402 288.015 231.437C288.464 232.309 288.734 233.182 289.004 234.054C289.094 234.442 289.184 234.732 289.364 235.12C289.364 235.12 288.914 238.028 288.914 238.124C288.644 238.609 288.195 239.481 287.475 240.063C287.565 240.547 287.655 241.129 287.565 241.71C287.385 242.68 287.205 244.133 286.126 245.199C285.137 246.265 283.697 246.362 283.158 246.362C282.438 246.362 281.719 246.169 281.089 245.781C280.999 245.781 280.999 245.684 280.909 245.684C280.37 246.072 279.56 246.459 278.481 246.459C278.391 246.459 278.301 246.459 278.211 246.459C275.962 246.362 274.254 245.006 273.174 242.583C272.904 242.583 272.725 242.68 272.455 242.68C271.375 242.68 269.936 242.292 269.037 241.032C268.138 239.772 267.238 238.318 266.339 236.671C265.529 235.217 264.81 233.763 264.09 232.309L263.461 230.953C263.371 230.759 263.281 230.565 263.191 230.371C261.842 228.433 260.942 226.301 260.223 224.265C260.133 223.975 259.953 223.587 259.863 223.296C259.143 221.358 258.244 218.935 258.514 216.221V214.574C258.514 212.635 258.424 210.697 258.424 208.759C257.704 208.468 256.805 207.886 256.175 206.627C254.826 203.913 253.477 201.102 252.218 198.389C250.059 194.027 247.901 189.472 245.742 185.014C242.414 177.939 239.626 170.767 237.467 163.692C237.287 163.111 236.928 162.432 236.568 161.754C236.298 161.269 236.028 160.688 235.758 160.203C235.578 159.719 235.309 159.234 235.129 158.847C234.409 157.393 233.69 155.939 233.24 154.195C232.79 152.741 232.79 151.287 232.79 150.027C232.79 149.639 232.79 149.349 232.79 148.961C232.79 148.38 232.88 147.798 233.15 147.217C232.341 146.441 231.531 145.569 230.991 144.503C229.283 141.498 227.754 138.494 226.314 135.587C225.775 134.52 224.606 132.194 224.606 132.098C224.606 132.098 224.336 134.617 224.246 135.393C223.256 143.534 221.727 149.639 219.389 155.067C220.468 155.454 221.368 156.424 221.637 157.78C222.357 160.979 222.987 164.274 223.706 167.86C223.976 169.023 223.526 170.38 222.807 171.349C223.976 173.675 224.606 176.195 225.235 178.618C225.955 181.622 226.494 184.723 227.034 187.728L227.304 188.988C227.484 190.054 227.214 191.023 227.034 191.508L226.944 191.701C226.944 191.701 226.674 197.129 226.584 197.904C228.203 198.582 229.283 200.133 229.642 202.362C230.092 205.754 230.632 209.728 230.901 213.701C231.261 219.226 231.351 223.684 231.261 227.754V228.627C231.081 235.411 230.991 242.389 230.272 249.173C229.642 255.376 228.743 261.578 227.933 267.684C227.574 270.107 227.214 272.627 226.854 275.05C226.764 275.825 226.584 276.6 226.494 277.279L226.404 277.763L224.696 288.133V288.424C224.696 290.653 224.875 292.882 225.055 295.208C225.145 297.244 225.325 299.376 225.415 301.411C225.505 303.64 225.235 305.772 224.965 307.905L224.875 308.389C224.785 309.358 224.426 310.327 224.156 311.006C224.066 311.2 223.976 311.49 223.886 311.684L223.706 312.072C223.796 312.363 223.976 312.653 224.066 312.944C225.775 316.724 227.034 319.341 228.473 324.962C228.743 325.834 229.013 326.803 229.193 327.676C230.002 330.389 230.812 333.2 231.081 336.107C231.351 338.433 231.261 342.988 231.081 344.927C230.991 345.702 230.902 346.671 230.542 347.64V349.094C230.542 352.583 229.552 355.781 228.653 358.689C228.473 359.173 228.383 359.561 228.203 360.046C225.595 368.574 223.706 377.588 221.817 386.213C220.918 390.187 220.828 393.579 221.368 396.68C221.637 398.425 221.817 400.169 221.997 401.817C222.087 402.301 222.087 402.883 222.177 403.368C222.267 404.046 222.177 404.627 222.087 404.918C222.087 405.015 222.087 405.112 222.087 405.112L221.997 405.79C223.616 405.694 225.865 405.5 227.843 405.5C229.462 405.5 230.542 405.597 231.441 405.79C234.949 406.76 238.187 409.667 239.446 413.253C240.615 416.548 239.986 420.134 237.647 422.945C235.399 425.658 232.61 426.531 230.092 427.209C228.203 427.79 226.584 428.275 225.505 429.438C225.145 429.826 224.695 430.407 224.336 431.086C223.886 431.764 223.436 432.443 222.897 433.024C221.008 435.447 215.611 436.416 212.463 436.416Z"
            fill="white"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M131.965 104.476C133.045 104.573 134.124 104.767 134.664 104.767C139.97 104.864 144.198 103.119 147.705 99.727C150.494 97.0133 152.922 93.912 155.71 91.1983C157.149 89.7445 158.768 88.6785 160.387 87.4185C161.017 86.934 161.287 86.4494 161.287 85.5771C161.017 81.2159 157.149 76.0793 153.282 75.1101C152.562 74.9163 151.573 74.9163 150.853 75.207C145.547 77.1454 140.78 79.9559 137.002 84.4141C134.034 87.9031 132.595 92.1675 131.876 96.7226C131.516 98.8547 131.426 100.987 131.156 103.119C131.066 103.991 131.336 104.379 131.965 104.476Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M167.492 37.9914C167.852 38.8636 168.302 39.8328 168.841 40.6081C169.111 40.9958 169.741 41.0927 170.101 41.0927H171.54C171.72 42.2557 171.81 43.3218 171.899 44.4848C172.169 46.52 172.259 48.4584 172.529 50.4936C172.709 51.9474 173.608 52.6258 174.868 53.3042C174.958 52.8196 175.047 52.432 175.047 52.1412C175.407 50.009 175.587 47.78 176.037 45.6478C176.666 42.7403 177.566 39.9297 179.904 38.0883C183.862 34.89 188.179 34.6962 192.586 37.0222C194.925 38.1852 196.184 40.3174 196.993 42.6434C197.713 44.8725 198.163 47.1985 198.612 49.5245C198.882 50.6875 198.882 51.9474 199.062 53.4011C200.141 52.7227 201.041 52.1412 201.311 50.8813C201.67 48.943 201.76 47.0046 201.94 45.0663C202.12 43.7095 202.3 42.3526 202.48 40.9958H203.829C204.279 40.802 204.818 40.6081 205.178 40.4143C205.358 40.3174 205.448 39.9297 205.628 39.639C206.617 37.4099 207.606 35.1808 208.596 32.8548C208.686 32.564 208.776 32.2733 208.866 32.0794C208.866 31.1103 207.427 29.1719 206.617 29.1719L204.369 28.2028C204.638 24.8107 204.548 21.4186 203.829 17.9296C201.94 8.43176 193.755 2.03525 184.941 3.29517C181.343 3.77975 178.105 5.2335 175.407 7.94717C171.18 12.2115 169.651 17.7358 169.381 23.7446C169.291 25.3922 169.381 26.9429 169.561 28.5904L167.402 29.2689C166.053 29.2689 164.794 31.6918 165.334 32.9517C166.143 34.6962 166.863 36.3438 167.492 37.9914Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M212.014 85.7713C211.924 86.5467 212.283 87.1282 212.913 87.6128C214.532 88.7758 216.151 89.9388 217.59 91.3925C220.288 94.1062 222.807 97.2075 225.595 99.9212C229.103 103.313 233.33 105.058 238.636 104.961C239.176 104.864 240.255 104.767 241.335 104.67C241.964 104.573 242.324 104.186 242.234 103.507C241.964 101.375 241.874 99.1458 241.515 97.1106C240.795 92.5555 239.356 88.2912 236.388 84.8022C232.52 80.4409 227.754 77.5334 222.537 75.5951C221.817 75.3043 220.918 75.3043 220.108 75.4982C216.151 76.2735 212.283 81.4101 212.014 85.7713Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M166.322 166.115C166.233 165.921 166.053 165.824 165.873 165.727C165.603 165.534 165.423 165.437 165.153 165.243C164.883 165.049 164.614 165.146 164.074 165.049C160.206 165.34 156.968 167.278 154.54 170.767C152.741 173.287 151.842 176.388 151.122 179.49C150.313 182.882 149.773 186.274 149.144 189.666C149.054 190.054 149.323 190.538 149.413 191.023C149.863 190.829 150.403 190.829 150.583 190.441C151.392 189.278 152.292 188.018 152.921 186.758C153.101 186.274 153.371 185.886 153.551 185.498L157.058 181.719L159.037 180.362C159.757 179.974 160.386 179.587 161.106 179.296C163.804 178.327 166.682 177.648 169.47 176.873C170.64 176.582 170.91 176.388 170.64 175.128C170.1 173.19 169.47 171.252 168.841 169.313C168.661 168.732 168.391 168.441 168.121 168.247V168.15L166.322 166.115Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M156.969 214.38C156.879 210.794 156.429 207.208 154.361 204.01C153.461 202.556 152.202 201.296 150.493 200.715C148.425 200.036 147.165 200.618 146.896 202.653C146.356 206.336 145.906 210.019 145.636 213.701C145.367 218.257 145.187 222.909 145.277 227.561C145.457 234.635 145.546 241.71 146.266 248.785C147.076 257.411 148.515 265.94 149.684 274.565C149.774 275.437 150.044 276.31 150.134 277.182L151.932 287.843C151.932 288.037 151.932 288.23 151.932 288.424C151.842 292.786 151.393 297.147 151.303 301.508C151.213 303.64 151.573 305.869 151.842 308.001C151.932 308.971 152.382 309.843 152.652 310.715C152.742 310.715 152.922 310.715 153.012 310.715C153.102 310.521 153.192 310.327 153.281 310.134C154.811 306.354 156.16 302.574 157.958 298.891C161.376 291.816 163.085 284.354 163.445 276.503C163.715 270.301 163.895 264.195 164.164 257.992C164.254 256.151 164.614 254.213 164.704 252.371C165.064 247.622 165.424 242.873 165.514 238.124C165.604 235.411 165.424 232.697 165.154 229.983C165.064 228.53 164.524 228.142 163.085 228.627C162.186 228.917 161.286 229.208 160.387 229.596C158.858 230.177 155.17 231.146 155.08 228.724C156.16 224.072 157.059 219.323 156.969 214.38Z"
            fill={
              selectedMuscles?.includes("hamstrings") ? COLORS.red : "#C1C1C1"
            }
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M162.366 309.843C162.816 309.456 163.176 309.165 163.445 308.971C164.345 308.196 165.244 307.226 166.234 306.548C167.223 305.87 167.403 305.482 166.683 304.416C164.974 301.605 163.535 298.698 162.816 295.403C162.816 295.209 162.636 295.112 162.456 294.627C161.377 296.759 160.477 298.698 159.488 300.539C159.128 301.314 159.308 301.993 159.668 302.768C160.387 304.319 161.017 305.87 161.647 307.42C161.916 308.099 162.096 308.874 162.366 309.843Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M250.509 150.028C253.837 153.807 256.805 157.878 259.323 162.239C259.503 162.53 259.683 162.724 259.863 162.917C259.953 162.821 260.043 162.821 260.133 162.724C260.043 154.389 255.996 147.411 251.858 140.627C251.678 140.627 251.588 140.627 251.409 140.627C251.229 141.014 250.959 141.402 250.869 141.79C250.509 143.825 250.149 145.86 249.79 147.798C249.7 148.671 249.97 149.349 250.509 150.028Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M217.591 158.459C216.152 160.204 214.802 162.045 213.453 163.886C214.802 164.953 216.152 165.825 217.411 166.891C218.13 167.569 218.76 168.442 219.479 169.12C219.749 169.411 220.289 169.605 220.559 169.508C220.829 169.411 221.098 168.732 221.008 168.442C220.379 165.049 219.659 161.754 218.94 158.459C218.76 157.684 218.13 157.684 217.591 158.459Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M221.908 131.516C221.818 131.516 221.728 131.419 221.638 131.419C221.458 131.807 221.368 132.098 221.188 132.485C220.199 134.424 219.21 136.459 218.04 138.3C216.871 140.239 215.342 141.886 213.184 142.468C210.215 143.34 207.337 143.049 204.459 141.789C204.189 141.692 203.92 141.595 203.47 141.402C206.978 148.186 208.686 155.358 210.036 163.014C210.755 162.432 211.295 162.142 211.744 161.754C213.903 159.622 215.432 157.005 216.691 154.195C219.39 148.089 220.739 141.595 221.548 134.908C221.638 133.842 221.728 132.679 221.908 131.516Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M213.363 229.596C212.464 229.208 211.565 228.917 210.665 228.627C209.226 228.142 208.686 228.53 208.596 229.983C208.417 232.697 208.147 235.411 208.237 238.124C208.417 242.873 208.776 247.622 209.046 252.371C209.136 254.213 209.496 256.151 209.586 257.992C209.856 264.098 210.125 270.301 210.305 276.503C210.575 284.354 212.284 291.816 215.792 298.891C217.591 302.574 218.94 306.354 220.469 310.134C220.559 310.327 220.649 310.521 220.739 310.715C220.829 310.715 221.008 310.715 221.098 310.715C221.368 309.843 221.818 308.971 221.908 308.001C222.178 305.869 222.448 303.64 222.448 301.508C222.358 297.147 221.908 292.786 221.818 288.424C221.818 288.23 221.818 288.037 221.818 287.843L223.617 277.182C223.797 276.31 223.977 275.437 224.066 274.565C225.236 266.036 226.585 257.411 227.484 248.785C228.204 241.71 228.294 234.635 228.474 227.561C228.564 222.909 228.384 218.353 228.114 213.701C227.844 210.019 227.394 206.336 226.855 202.653C226.585 200.618 225.326 200.036 223.257 200.715C221.548 201.296 220.289 202.556 219.39 204.01C217.411 207.208 216.871 210.697 216.781 214.38C216.601 219.226 217.591 223.975 218.67 228.724C218.58 231.243 214.892 230.177 213.363 229.596Z"
            fill={
              selectedMuscles?.includes("hamstrings") ? COLORS.red : "#C1C1C1"
            }
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M187.639 191.023C187.639 193.737 187.549 196.547 187.549 199.261C187.549 199.552 187.729 199.939 187.819 200.23C188.088 200.036 188.538 199.939 188.718 199.649C189.707 198.001 190.787 196.45 191.686 194.706C194.384 189.666 195.733 184.142 197.442 178.715C198.612 174.935 199.781 171.155 201.76 167.86C202.929 165.922 204.188 164.08 206.347 163.402C207.876 162.917 207.876 162.917 207.786 161.173C207.336 154.485 205.627 148.186 201.939 142.759C199.151 138.688 195.733 135.102 192.586 131.225C192.316 130.935 191.956 130.741 191.596 130.45C190.967 130.159 190.337 130.256 190.157 130.935C189.348 133.455 188.358 135.974 188.088 138.591C187.729 142.371 187.819 146.248 187.819 150.124C187.819 157.393 187.819 164.662 187.819 171.93H187.729C187.639 178.424 187.729 184.723 187.639 191.023Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M154.271 169.119C154.991 168.441 155.53 167.472 156.34 166.89C157.599 165.824 158.948 164.855 160.297 163.886C158.948 162.141 157.599 160.3 156.16 158.459C155.62 157.683 154.991 157.78 154.811 158.459C154.091 161.754 153.372 165.146 152.742 168.441C152.652 168.732 152.922 169.41 153.192 169.507C153.462 169.701 154.001 169.41 154.271 169.119Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M207.517 306.451C208.506 307.227 209.316 308.099 210.305 308.874C210.575 309.165 210.935 309.359 211.385 309.746C211.654 308.777 211.834 308.002 212.104 307.226C212.734 305.676 213.273 304.125 214.083 302.575C214.443 301.799 214.622 301.121 214.263 300.345C213.273 298.407 212.374 296.566 211.295 294.433C211.115 294.918 210.935 295.015 210.935 295.209C210.215 298.407 208.776 301.411 207.067 304.222C206.438 305.385 206.618 305.773 207.517 306.451Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M204.189 176.679C206.977 177.455 209.855 178.133 212.553 179.102C213.273 179.393 213.992 179.781 214.622 180.168L216.601 181.525L220.108 185.305C220.288 185.693 220.558 186.177 220.738 186.565C221.368 187.922 222.177 189.085 223.077 190.248C223.346 190.538 223.886 190.635 224.246 190.829C224.336 190.345 224.606 189.957 224.516 189.472C223.886 186.08 223.256 182.688 222.537 179.296C221.817 176.195 220.918 173.19 219.119 170.573C216.691 167.084 213.453 165.146 209.585 164.855C209.136 164.952 208.776 164.952 208.506 165.049C208.326 165.243 208.056 165.34 207.786 165.534C207.607 165.631 207.427 165.825 207.337 165.921L205.358 167.763V167.86C205.088 168.054 204.908 168.441 204.638 168.926C204.009 170.864 203.379 172.803 202.84 174.741C202.75 176.195 203.019 176.388 204.189 176.679Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M202.391 321.57C202.57 321.57 202.84 321.473 203.02 321.473L203.11 311.587C203.11 310.618 203.2 309.746 203.2 308.777C203.2 307.42 203.65 306.451 204.549 305.385C207.427 301.993 209.496 298.213 210.216 293.948C210.395 292.785 210.305 291.622 209.946 290.556L208.327 273.111C208.327 272.724 208.327 272.336 208.327 271.948C208.327 267.587 207.967 263.226 207.697 258.865C207.337 253.922 206.978 248.882 206.528 243.939C206.168 239.772 205.808 235.605 205.179 231.437C204.639 227.851 202.93 224.847 199.153 223.296C196.904 222.327 194.656 221.358 192.947 219.516C192.317 218.838 191.328 219.226 191.238 220.195C191.238 220.389 191.238 220.486 191.238 220.679V223.878C191.238 225.234 190.068 242.001 190.158 243.358C190.698 253.05 190.788 266.23 191.238 275.825C191.508 281.155 192.947 289.49 194.656 296.565C196.634 304.803 197.354 309.94 201.131 319.438C201.671 320.019 202.031 320.794 202.391 321.57Z"
            fill={
              selectedMuscles?.includes("hamstrings") ? COLORS.red : "#C1C1C1"
            }
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M193.845 71.3307L198.702 70.0708C201.76 70.1677 204.728 70.1677 207.786 70.1677C207.966 70.1677 208.056 70.0708 208.506 69.9739C208.056 69.6831 207.786 69.4893 207.607 69.3924C205.808 68.2294 203.919 67.0664 202.12 65.8065C199.782 64.2558 198.702 61.9298 198.612 59.0223C198.522 56.5025 198.253 53.9826 198.163 51.4628C197.983 47.683 196.993 44.194 195.284 40.8989C195.105 40.6081 194.925 40.4143 194.655 40.1235C194.565 40.1235 194.385 40.2205 194.295 40.2205C194.655 45.1632 195.105 50.2029 195.464 55.1456C195.374 54.8549 195.284 54.5641 195.284 54.2734C194.655 49.5245 194.115 44.6786 193.486 39.9297C193.396 39.4451 193.126 38.6698 192.676 38.476C189.258 36.3438 185.841 36.3438 182.333 38.1852C181.253 38.7667 180.984 39.4451 180.894 40.705C180.624 46.1324 179.994 51.4628 178.645 56.6963C178.465 57.2778 178.285 57.7624 178.106 58.3439C179.275 50.6875 180.084 41.5773 179.455 40.3174C179.185 40.705 178.915 41.0927 178.645 41.4804C177.296 43.9033 176.487 46.52 176.217 49.3306C175.947 52.9165 175.767 56.5025 175.407 60.0884C175.137 62.5113 174.328 64.6435 172.349 66.0972C170.64 67.2602 168.841 68.4232 167.133 69.4893C166.863 69.6831 166.503 69.877 165.963 70.1677C166.413 70.2647 166.593 70.3616 166.773 70.3616C170.011 70.2646 173.159 70.2647 176.397 70.1677L180.624 71.5246C184.941 75.2074 186.65 80.0532 187.19 85.8682C187.909 82.9607 188.449 80.344 189.258 77.9211C189.888 75.2074 191.687 73.2691 193.845 71.3307Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M186.2 143.631C186.11 143.631 186.11 139.463 185.751 137.525C185.391 135.49 184.581 133.454 183.862 131.516C183.502 130.353 182.872 130.159 181.883 130.838C181.523 131.128 181.164 131.516 180.804 131.807C179.455 133.357 178.195 135.005 176.846 136.556C175.407 138.3 173.968 139.948 172.619 141.789C170.73 144.212 169.471 147.12 168.392 150.027C166.953 153.807 166.233 157.78 166.053 161.948C166.053 162.82 166.323 163.208 167.043 163.402C168.572 163.789 169.741 164.662 170.73 166.018C173.429 169.41 174.688 173.578 176.037 177.745C177.296 181.816 178.555 185.886 179.994 189.957C181.253 193.543 182.962 196.935 185.121 199.939C185.301 200.23 185.661 200.327 185.93 200.521C186.02 200.23 186.29 199.842 186.29 199.552C186.29 198.776 186.2 198.001 186.2 197.226C186.2 179.296 186.2 161.463 186.2 143.631Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M230.901 408.795C228.563 408.213 222.087 408.892 220.558 408.989C218.849 406.469 218.759 406.372 217.32 402.98C216.781 401.72 216.331 400.46 215.881 399.2C215.162 396.971 210.934 395.517 208.866 396.293C206.707 397.165 205.898 399.103 205.448 401.235C204.818 404.046 205.088 406.857 205.448 409.57C205.718 412.284 206.077 414.998 206.437 417.614C205.808 423.139 205.448 426.821 207.876 432.346C211.654 434.284 219.479 432.733 221.008 430.892C221.997 429.729 222.717 428.372 223.706 427.209C227.034 423.526 232.52 424.786 235.758 420.813C237.557 418.583 237.737 416.257 237.018 414.222C236.028 411.605 233.42 409.473 230.901 408.795Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M271.465 202.847C272.455 203.041 272.635 202.556 272.275 201.49C270.296 195.675 268.227 189.86 266.339 183.948C265.439 181.137 264.81 178.23 264 175.419C263.91 175.032 263.64 174.838 263.371 174.45C263.101 174.741 262.741 175.032 262.561 175.322C262.381 175.613 262.381 176.001 262.381 176.292C262.291 178.23 262.111 180.168 262.111 182.107C262.111 185.789 262.291 189.472 262.381 193.252C262.291 193.252 262.111 193.252 262.021 193.252C261.032 187.243 260.762 181.234 261.032 175.225C261.212 169.217 260.133 163.789 256.445 159.04C256.175 158.75 255.995 158.459 255.726 158.071C255.636 157.877 255.456 157.78 255.366 157.587L252.578 154.291C251.318 153.322 250.239 152.547 249.16 151.675L247.001 149.543C246.911 149.446 246.821 149.349 246.731 149.349C246.461 149.155 246.012 149.155 245.472 149.349C244.573 149.543 243.673 150.124 242.864 150.706C241.964 151.287 241.784 152.256 242.234 153.225C242.684 154.291 243.224 155.358 243.853 156.327C245.022 158.071 246.282 159.816 246.911 161.948C249.07 169.314 251.318 176.776 253.567 184.142C254.196 186.274 254.826 188.503 255.456 190.635C255.366 190.635 255.366 190.635 255.276 190.732C255.186 190.635 255.096 190.538 255.096 190.344C252.757 182.882 250.509 175.322 248.17 167.86C247.091 164.371 246.102 160.785 243.853 157.684C242.504 155.842 241.155 154.001 239.806 152.159C238.816 150.899 237.827 149.639 236.838 148.38C236.658 148.186 236.298 147.895 236.028 147.992C235.848 148.089 235.668 148.476 235.668 148.767C235.668 150.221 235.578 151.772 235.938 153.128C236.478 155.067 237.467 156.908 238.367 158.75C238.996 160.01 239.716 161.269 240.166 162.626C242.324 169.798 245.112 176.776 248.26 183.56C251.678 190.829 255.186 197.904 258.694 205.076C259.143 205.948 259.683 205.948 260.402 205.56C264 203.816 267.328 201.975 271.465 202.847Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M291.433 218.741C288.645 217.384 285.857 216.027 284.058 213.411C283.428 212.538 282.799 211.666 282.079 210.794C280.19 208.371 278.122 206.142 275.244 204.882C274.794 204.688 274.344 204.591 273.984 204.494L269.667 204.107C269.667 204.107 269.667 204.107 269.667 204.204C268.948 204.107 268.318 204.204 267.598 204.494C265.71 205.27 263.821 206.239 261.932 207.208C261.662 207.402 261.302 207.886 261.302 208.274C261.302 210.988 261.392 213.604 261.392 216.318C261.033 218.644 262.112 220.873 262.921 223.005C263.731 225.234 264.45 227.173 265.8 228.723C265.89 229.014 265.979 229.208 265.979 229.402C266.879 231.243 267.778 233.182 268.768 235.023C269.487 236.38 270.297 237.834 271.286 239.094C271.556 239.481 272.365 239.675 272.815 239.481C273.535 239.191 273.175 238.512 272.995 237.931C272.185 236.186 271.376 234.442 270.656 232.697C270.567 232.406 270.387 232.116 270.297 231.825C270.656 231.631 270.926 231.243 271.286 230.662C271.646 231.631 272.096 232.503 272.545 233.472C273.625 235.895 274.614 238.318 275.603 240.838C276.143 242.195 276.862 243.261 278.391 243.358C279.291 243.455 279.651 242.97 279.651 241.904C279.561 241.71 279.471 241.323 279.381 241.032C278.212 238.124 276.952 235.217 275.693 232.406C275.154 231.243 274.524 229.983 273.894 228.82C274.074 228.53 274.344 228.336 274.614 228.045C275.154 229.111 275.693 230.274 276.233 231.34C277.762 234.635 279.201 237.931 280.82 241.226C281.18 242.001 281.899 242.68 282.619 243.067C282.979 243.358 283.878 243.164 284.148 242.873C284.508 242.486 284.687 241.71 284.777 241.032C284.867 240.741 284.597 240.354 284.418 239.966C282.079 235.023 279.831 230.177 277.492 225.234C277.672 225.041 277.852 224.75 278.032 224.556C280.19 228.53 282.259 232.6 284.418 236.574C284.687 236.961 285.407 237.543 285.767 237.349C286.216 237.155 286.486 236.38 286.756 235.895C286.306 234.635 286.037 233.569 285.587 232.503C284.328 229.79 282.979 227.076 281.629 224.362C280.91 222.908 280.28 221.552 279.561 220.098C279.561 220.001 279.471 219.904 279.471 219.71C279.92 218.838 280.82 218.547 280.82 218.547C281.18 219.226 281.989 220.001 282.709 220.195C284.508 220.873 286.396 221.261 288.285 221.649C289.724 221.939 290.983 221.455 291.883 220.195C292.512 219.516 292.422 219.226 291.433 218.741Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M172.889 179.393C172.44 177.648 171.54 177.067 169.561 177.357C165.424 177.939 162.006 179.683 159.128 182.3C154.811 186.177 152.113 191.023 150.134 196.256C149.594 197.71 149.864 198.388 151.483 199.164C154.091 200.521 155.71 202.459 156.52 205.076C157.689 208.952 158.409 212.829 158.229 216.803C158.139 219.71 157.689 222.618 157.419 225.525C157.149 227.56 158.139 228.336 160.207 227.657C165.604 226.107 170.731 223.974 175.408 220.97C179.635 218.256 183.323 215.058 185.481 210.6C186.65 208.274 186.56 206.336 184.941 204.01C179.455 196.45 175.228 188.309 172.889 179.393Z"
            fill={selectedMuscles?.includes("glutes") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M198.342 220.97C203.019 223.974 208.146 226.107 213.542 227.657C215.611 228.239 216.69 227.463 216.33 225.525C216.061 222.618 215.611 219.71 215.521 216.803C215.341 212.829 215.971 208.855 217.23 205.076C218.039 202.556 219.748 200.521 222.267 199.164C223.886 198.388 224.155 197.71 223.616 196.256C221.637 191.023 219.029 186.177 214.622 182.3C211.743 179.683 208.236 177.842 204.188 177.357C202.21 177.067 201.31 177.648 200.86 179.393C198.522 188.309 194.295 196.45 188.808 204.106C187.189 206.432 187.099 208.371 188.269 210.697C190.427 215.058 194.025 218.256 198.342 220.97Z"
            fill={selectedMuscles?.includes("glutes") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M111.279 207.111C109.39 206.142 107.592 205.173 105.613 204.398C104.893 204.107 104.264 204.01 103.544 204.107V204.01L99.227 204.398C98.7773 204.495 98.3276 204.592 97.9678 204.785C95.0896 206.045 93.021 208.274 91.1322 210.697C90.5026 211.57 89.7831 212.442 89.1535 213.314C87.3546 216.028 84.5664 217.385 81.7782 218.645C80.7889 219.129 80.6989 219.517 81.3285 220.389C82.2279 221.649 83.4871 222.134 84.9262 221.843C86.815 221.455 88.7038 220.971 90.5026 220.389C91.2221 220.098 91.9417 219.42 92.3914 218.741C92.3914 218.741 93.2908 219.032 93.7405 219.904C93.6506 220.001 93.6506 220.098 93.6506 220.292C92.931 221.649 92.2115 223.103 91.5819 224.556C90.2328 227.27 88.8836 229.887 87.6245 232.698C87.1747 233.667 86.9049 234.83 86.4552 236.09C86.815 236.574 86.9949 237.35 87.4446 237.543C87.7144 237.64 88.5239 237.156 88.7937 236.768C90.9523 232.794 93.1109 228.724 95.1796 224.75C95.3595 224.944 95.5394 225.235 95.7192 225.429C93.3808 230.275 91.0423 235.217 88.7937 240.16C88.6138 240.451 88.4339 240.839 88.4339 241.226C88.5239 241.905 88.6138 242.68 89.0635 243.068C89.3334 243.358 90.2328 243.455 90.5925 243.261C91.3121 242.777 91.9417 242.195 92.3914 241.42C94.0103 238.125 95.4494 234.83 96.9784 231.534C97.5181 230.468 98.0577 229.305 98.5974 228.239C98.8672 228.53 99.137 228.724 99.3169 229.015C98.6873 230.178 98.0577 231.341 97.5181 232.601C96.2589 235.411 95.0896 238.319 93.8305 241.226C93.6506 241.517 93.5606 241.905 93.5606 242.098C93.5606 243.165 94.0103 243.649 94.8198 243.552C96.3488 243.455 97.0684 242.389 97.608 241.032C98.5974 238.609 99.6767 236.187 100.666 233.667C101.026 232.698 101.476 231.825 101.925 230.856C102.285 231.438 102.645 231.728 102.915 232.019C102.735 232.31 102.645 232.504 102.555 232.891C101.925 234.733 101.026 236.38 100.216 238.125C99.9465 238.609 99.6767 239.385 100.396 239.676C100.846 239.869 101.655 239.676 101.925 239.288C102.825 238.028 103.634 236.671 104.444 235.217C105.433 233.376 106.332 231.438 107.232 229.596C107.322 229.402 107.412 229.208 107.412 228.918C108.671 227.27 109.48 225.332 110.29 223.2C111.099 221.067 112.179 218.838 111.819 216.512C111.909 213.799 111.909 211.182 111.909 208.468C111.909 207.887 111.639 207.305 111.279 207.111Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M113.977 162.239C116.496 157.878 119.464 153.807 122.792 150.028C123.331 149.349 123.601 148.671 123.421 147.798C123.062 145.763 122.702 143.728 122.342 141.79C122.252 141.402 121.982 141.014 121.802 140.627C121.622 140.627 121.533 140.627 121.353 140.627C117.215 147.508 113.168 154.389 113.078 162.724C113.168 162.821 113.258 162.821 113.348 162.917C113.618 162.724 113.887 162.53 113.977 162.239Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M170.281 141.498C169.921 141.595 169.831 141.595 169.741 141.595C167.762 142.565 165.604 142.952 163.445 142.855C161.106 142.758 158.768 142.274 157.239 140.045C155.71 137.719 154.271 135.296 152.832 132.873C152.562 132.388 152.472 131.904 152.292 131.419C152.202 131.419 152.112 131.419 152.022 131.516C152.022 131.71 151.932 131.904 152.022 132.001C152.832 137.525 153.371 143.049 155.08 148.38C156.34 152.353 157.869 156.133 160.207 159.428C160.927 160.397 161.736 161.269 162.635 162.142C163.625 163.111 163.985 162.917 164.164 161.56C164.164 161.463 164.164 161.366 164.254 161.269C165.154 155.842 166.503 150.415 168.662 145.375C169.111 144.115 169.651 142.952 170.281 141.498Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M179.994 130.159C181.793 127.736 181.973 126.767 180.444 124.15C178.105 120.177 175.677 116.397 173.339 112.52L171.54 109.128H171.36C171.27 108.838 171.09 108.644 170.91 108.353C169.741 106.512 168.482 104.864 167.402 102.926C165.244 99.2428 163.895 95.1723 163.265 90.811C162.995 89.1634 162.186 88.7757 160.837 89.648C160.297 90.0357 159.757 90.4233 159.218 90.811C154.99 94.4938 152.202 99.2428 150.763 104.864C150.673 105.349 150.583 105.736 150.583 106.027L150.403 110.291C150.313 111.939 150.223 113.683 150.134 115.331C150.223 116.979 150.223 118.529 150.403 120.177C150.853 125.798 152.472 131.031 155.62 135.587C157.509 138.397 160.117 140.335 163.265 141.111C166.323 141.983 169.021 140.82 171.54 138.979C174.778 136.653 177.476 133.551 179.994 130.159Z"
            fill={selectedMuscles?.includes("back") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M182.873 77.5333C181.704 74.6258 179.995 72.3968 176.757 71.8153C172.799 71.1368 168.842 70.8461 164.974 71.5245C161.826 72.106 158.678 73.1721 155.53 74.0443C155.53 74.2382 155.53 74.3351 155.53 74.5289C160.207 75.9827 162.006 79.9563 163.086 84.5113C163.535 86.2558 163.805 88.0004 164.165 89.7449C164.974 93.4277 165.694 97.2074 167.673 100.406C170.821 105.542 174.148 110.679 177.476 115.719C180.444 120.274 183.592 124.635 185.121 130.062C185.481 131.225 185.661 132.388 186.021 133.745C186.111 131.904 186.291 130.256 186.291 128.705C186.201 116.591 186.021 104.573 185.931 92.4585C185.931 87.225 184.762 82.2823 182.873 77.5333Z"
            fill={selectedMuscles?.includes("back") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M223.526 110.291L223.346 106.027C223.346 105.736 223.256 105.445 223.166 105.155C222.897 104.186 222.627 103.216 222.267 102.247C220.558 97.2075 217.59 93.0401 213.543 89.9387C211.744 88.5819 210.934 88.9696 210.575 91.2956C210.035 94.6877 209.136 97.9828 207.517 100.987C206.077 103.604 204.459 106.027 202.84 108.45L200.591 112.423C198.073 116.591 195.554 120.758 192.946 124.926C192.047 126.379 192.047 127.833 193.036 129.19C195.914 133.454 199.332 137.137 203.559 139.948C206.707 141.983 209.945 141.886 213.183 140.239C216.691 138.397 218.849 135.199 220.558 131.516C222.897 126.476 223.616 121.146 223.616 114.749C223.706 113.78 223.616 112.036 223.526 110.291Z"
            fill={selectedMuscles?.includes("back") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M198.703 71.5248C195.825 71.8156 193.216 72.7847 191.687 75.7891C189.978 79.1812 188.719 82.8641 188.359 86.7407C187.73 92.5557 187.64 98.3708 187.55 104.186C187.46 112.521 187.55 120.952 187.55 129.287C187.55 130.644 187.64 132.001 187.73 133.261C188.899 126.38 192.677 120.952 196.364 115.622C198.703 112.23 200.951 108.935 203.2 105.446C205.538 101.86 207.607 98.08 208.596 93.8157C209.316 90.5205 209.945 87.1284 210.755 83.8332C211.564 80.5381 212.914 77.5336 215.792 75.6922C216.511 75.2076 217.321 74.9169 218.13 74.4323C218.13 74.3354 218.13 74.2385 218.13 74.1416C215.702 73.3662 213.273 72.5909 210.755 71.9125C206.887 70.9433 202.84 71.1371 198.703 71.5248Z"
            fill={selectedMuscles?.includes("back") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          // Calves
          <Path
            d="M161.916 361.403C160.387 359.949 159.577 358.107 158.948 356.169C158.768 355.684 158.678 355.394 157.958 355.394C156.879 355.491 155.8 355.491 154.721 355.297C153.551 355.103 152.382 354.618 151.033 354.231C151.573 356.751 152.022 359.077 152.562 361.306C153.102 363.632 153.551 365.958 154.091 368.381C154.541 370.707 154.99 373.033 155.44 375.359C155.89 377.685 156.16 380.011 156.519 382.337C156.25 382.24 156.16 382.143 156.16 381.949C154.451 372.936 152.382 363.922 149.954 355.103C149.324 352.971 147.345 351.032 145.816 349.094C145.816 352.583 147.076 355.975 147.975 359.077C150.583 367.799 152.472 376.619 154.451 385.438C155.26 389.315 155.62 393.191 154.9 397.165C154.541 399.297 154.361 401.429 154.091 403.658C154.091 404.046 154.181 404.434 154.181 404.821C154.271 404.821 154.361 404.821 154.451 404.821C154.541 404.434 154.631 403.949 154.721 403.561C155.17 401.138 155.89 398.812 157.509 396.971C159.757 394.354 163.445 393.579 166.053 395.226C166.503 395.517 166.863 395.808 167.492 396.293C167.402 395.614 167.312 395.13 167.223 394.645C166.863 393.482 166.413 392.222 166.143 391.059C165.424 388.345 165.334 384.081 165.873 381.27C166.953 375.843 167.402 372.063 168.572 366.636C168.931 364.988 169.291 363.341 169.651 361.499C166.593 364.213 164.794 364.116 161.916 361.403Z"
            fill={selectedMuscles?.includes("calf") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M168.931 320.019C168.931 318.081 169.021 314.883 169.111 312.944C169.201 311.587 169.201 310.231 169.111 308.874C169.111 308.098 168.482 307.711 167.672 308.098C166.773 308.486 165.783 308.971 165.064 309.552C163.175 311.2 162.096 313.429 161.556 315.658C160.027 321.861 159.218 328.16 158.768 334.46C158.678 337.852 158.408 341.244 158.408 344.636C158.408 348.222 158.858 351.808 160.027 355.297C160.927 358.011 162.366 360.337 165.064 361.984C166.233 362.663 167.312 362.662 168.302 361.79C168.841 361.306 170.37 359.27 170.73 358.689C172.349 355.588 173.069 352.971 173.339 350.645C173.878 345.605 173.339 340.856 171.9 335.913C170.191 329.42 168.931 327.288 168.931 320.019Z"
            fill={selectedMuscles?.includes("calf") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M207.876 381.271C208.415 384.081 208.325 388.346 207.606 391.059C207.246 392.319 206.886 393.482 206.527 394.645C206.437 395.13 206.347 395.517 206.257 396.293C206.886 395.808 207.246 395.517 207.696 395.227C210.304 393.579 213.992 394.354 216.24 396.971C217.859 398.909 218.579 401.139 219.029 403.561C219.118 403.949 219.208 404.434 219.298 404.821C219.388 404.821 219.478 404.821 219.568 404.821C219.568 404.434 219.658 404.046 219.658 403.658C219.388 401.526 219.208 399.297 218.849 397.165C218.129 393.191 218.489 389.315 219.298 385.438C221.187 376.619 223.166 367.702 225.774 359.077C226.674 355.975 227.933 352.583 227.933 349.094C226.404 351.033 224.425 352.874 223.795 355.103C221.277 364.019 219.298 372.936 217.589 381.949C217.589 382.046 217.5 382.24 217.23 382.337C217.589 380.011 217.949 377.685 218.309 375.359C218.759 373.033 219.208 370.707 219.658 368.381C220.108 366.055 220.647 363.729 221.187 361.306C221.637 358.98 222.176 356.751 222.716 354.231C221.277 354.618 220.198 355.103 219.029 355.297C217.949 355.491 216.87 355.491 215.791 355.394C215.071 355.297 214.981 355.588 214.801 356.169C214.172 358.107 213.362 359.949 211.833 361.403C208.955 364.213 207.156 364.31 204.098 361.596C204.458 363.438 204.818 365.085 205.177 366.733C206.257 372.063 206.706 375.843 207.876 381.271Z"
            fill={selectedMuscles?.includes("calf") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M205.269 361.791C206.258 362.663 207.337 362.663 208.507 361.984C211.205 360.337 212.644 358.011 213.543 355.297C214.713 351.808 215.162 348.222 215.162 344.636C215.162 341.244 214.982 337.852 214.802 334.46C214.263 328.16 213.543 321.861 212.014 315.658C211.475 313.332 210.395 311.2 208.507 309.552C207.787 308.874 206.798 308.486 205.898 308.099C205.089 307.711 204.549 308.099 204.459 308.874C204.369 310.231 204.369 311.588 204.459 312.945C204.549 314.883 204.639 318.081 204.639 320.019C204.639 327.288 203.47 329.42 201.581 335.817C200.142 340.76 199.692 345.509 200.142 350.548C200.412 352.874 201.131 355.491 202.75 358.592C203.2 359.271 204.729 361.306 205.269 361.791Z"
            fill={selectedMuscles?.includes("calf") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M216.6 338.24C216.87 341.632 216.78 345.121 217.05 348.513C217.23 351.42 219.118 352.583 221.817 351.808C225.414 350.742 228.113 348.61 228.382 344.539C228.472 342.892 228.562 338.53 228.382 336.398C228.023 333.006 226.853 329.517 225.864 325.834C224.155 319.341 222.806 316.918 220.468 311.684C219.478 309.552 217.769 307.226 216.78 305.094C216.6 304.706 216.06 304.125 215.701 304.125C215.341 304.125 214.891 304.61 214.621 304.997C213.902 306.257 213.542 307.711 213.542 309.262C213.542 313.332 214.262 317.306 214.801 321.376C215.431 326.997 216.06 332.618 216.6 338.24Z"
            fill={selectedMuscles?.includes("calf") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M158.049 304.125C157.689 304.125 157.239 304.61 156.97 305.094C155.89 307.226 154.271 309.552 153.282 311.684C150.943 316.918 149.594 319.341 147.885 325.834C146.896 329.517 145.727 333.006 145.367 336.398C145.187 338.53 145.187 342.892 145.367 344.539C145.727 348.61 148.335 350.742 151.933 351.808C154.631 352.583 156.52 351.42 156.7 348.513C156.97 345.121 156.88 341.632 157.149 338.24C157.689 332.618 158.229 326.9 158.948 321.279C159.488 317.306 160.208 313.332 160.208 309.165C160.208 307.614 159.848 306.257 159.128 304.9C158.858 304.706 158.409 304.125 158.049 304.125Z"
            fill={selectedMuscles?.includes("calf") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          // Hamstring
          <Path
            d="M182.422 220.583C182.422 220.389 182.422 220.292 182.422 220.098C182.332 219.129 181.343 218.741 180.713 219.42C179.004 221.261 176.756 222.23 174.507 223.2C170.73 224.75 169.021 227.658 168.481 231.341C167.851 235.508 167.492 239.675 167.132 243.843C166.682 248.786 166.322 253.728 165.963 258.768C165.693 263.129 165.333 267.491 165.333 271.852C165.333 272.239 165.333 272.627 165.333 273.015L163.714 290.46C163.354 291.526 163.264 292.689 163.444 293.852C164.254 298.116 166.322 301.896 169.111 305.288C170.01 306.354 170.55 307.323 170.46 308.68C170.46 309.649 170.55 310.522 170.55 311.491L170.64 321.376C170.82 321.376 171.089 321.473 171.269 321.473C171.629 320.698 171.989 320.019 172.439 319.244C176.216 309.746 176.936 304.61 178.914 296.372C180.623 289.297 182.062 280.962 182.332 275.632C182.872 265.94 182.962 252.759 183.411 243.164C183.501 241.808 182.332 225.041 182.332 223.684L182.422 220.583Z"
            fill={
              selectedMuscles?.includes("hamstrings") ? COLORS.red : "#C1C1C1"
            }
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M137.542 148.767C137.542 148.476 137.362 147.992 137.182 147.992C137.002 147.895 136.552 148.186 136.372 148.38C135.383 149.639 134.394 150.899 133.404 152.159C132.055 154.001 130.706 155.842 129.357 157.684C127.108 160.688 126.119 164.274 125.04 167.86C122.701 175.322 120.453 182.785 118.114 190.344C118.114 190.441 118.024 190.635 117.934 190.732L117.754 190.635C118.384 188.503 119.014 186.274 119.643 184.142C121.892 176.776 124.14 169.41 126.299 161.948C126.928 159.816 128.188 158.071 129.357 156.327C129.987 155.358 130.526 154.291 130.976 153.225C131.426 152.256 131.246 151.287 130.346 150.706C129.537 150.221 128.637 149.639 127.738 149.349C127.198 149.252 126.749 149.252 126.479 149.349C126.389 149.446 126.299 149.446 126.209 149.543L124.05 151.675C122.971 152.45 121.892 153.322 120.633 154.291L117.844 157.587C117.485 158.071 117.125 158.556 116.765 158.943C113.077 163.692 111.998 169.12 112.178 175.129C112.358 181.137 112.178 187.243 111.189 193.155C111.099 193.155 110.919 193.155 110.829 193.155C110.919 189.472 111.099 185.789 111.099 182.01C111.099 180.071 110.919 178.133 110.829 176.195C110.829 175.904 110.829 175.516 110.649 175.225C110.469 174.838 110.109 174.644 109.839 174.353C109.66 174.644 109.3 174.935 109.21 175.322C108.4 178.133 107.771 181.04 106.871 183.851C104.983 189.763 102.914 195.578 100.935 201.393C100.575 202.459 100.755 202.944 101.745 202.75C105.792 201.878 109.21 203.719 112.628 205.464C113.347 205.851 113.887 205.851 114.337 204.979C117.844 197.807 121.352 190.635 124.77 183.463C127.918 176.679 130.706 169.701 132.865 162.529C133.314 161.173 134.124 160.01 134.663 158.653C135.473 156.811 136.462 154.97 137.092 153.032C137.632 151.772 137.452 150.221 137.542 148.767Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M164.794 396.293C162.725 395.42 158.498 396.971 157.778 399.2C157.328 400.46 156.879 401.72 156.339 402.98C154.9 406.372 154.72 406.469 153.101 408.989C151.572 408.892 145.096 408.213 142.758 408.795C140.239 409.473 137.631 411.606 136.732 414.319C136.012 416.354 136.192 418.68 137.991 420.91C141.229 424.883 146.805 423.623 150.043 427.306C151.032 428.372 151.752 429.826 152.741 430.989C154.27 432.83 162.005 434.381 165.873 432.443C168.301 426.821 167.942 423.236 167.312 417.711C167.672 414.998 167.942 412.381 168.301 409.667C168.571 406.857 168.931 404.046 168.301 401.332C167.762 399.103 167.042 397.165 164.794 396.293Z"
            fill="#C1C1C1"
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M129.896 124.15C133.404 121.533 136.552 118.82 138.171 114.749C139.25 112.036 140.689 109.322 141.948 106.608C142.128 106.124 142.308 105.639 142.578 104.767C141.049 105.154 139.97 105.445 138.8 105.639C136.282 106.221 133.764 106.511 131.245 105.93C130.166 105.639 129.716 106.221 129.446 106.996C128.457 109.516 127.288 112.036 126.568 114.652C125.939 116.784 125.759 119.014 125.399 121.146C125.309 121.63 125.399 122.212 125.579 122.696C126.209 125.022 127.917 125.701 129.896 124.15Z"
            fill={selectedMuscles?.includes("triceps") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M125.489 145.86C126.928 146.15 128.368 146.635 129.807 147.217C130.256 147.41 130.706 147.507 131.066 147.41L134.304 147.12C135.023 146.926 135.923 146.926 136.282 146.635C137.542 145.472 139.071 144.212 139.79 142.855C142.039 138.882 144.017 134.811 146.086 130.741C146.176 130.547 146.176 130.353 146.266 130.159C146.176 130.062 146.086 130.062 145.906 129.965C145.367 130.547 144.827 131.128 144.377 131.613C144.197 131.807 144.017 132.001 143.838 132.194C143.028 132.97 141.679 134.327 140.6 134.424C140.06 134.52 139.79 134.52 139.61 134.424L139.52 134.327C136.912 133.067 135.923 130.935 135.653 128.609C135.383 126.67 135.563 124.732 135.563 122.794C135.563 122.503 135.563 122.309 135.653 121.824C133.764 122.89 132.235 123.957 130.526 124.829C129.537 125.313 128.368 125.604 127.288 125.701C126.209 125.798 125.759 126.186 125.399 126.961C124.05 130.353 123.511 133.842 123.421 137.428C123.421 139.657 123.78 141.983 124.05 144.309C124.05 145.278 124.5 145.666 125.489 145.86Z"
            fill={selectedMuscles?.includes("triceps") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M235.489 130.741C235.578 130.547 235.578 130.353 235.668 130.159C235.848 128.221 236.028 126.379 236.118 125.023C235.848 123.181 235.578 121.921 235.489 120.758C234.679 113.974 230.812 108.547 226.944 103.216C226.584 102.732 225.775 102.538 225.145 102.538C224.785 102.538 224.516 103.41 224.336 103.895C224.246 104.186 224.246 104.476 224.246 104.864C224.246 109.031 224.156 113.199 224.336 117.366C224.516 123.084 227.124 127.833 230.991 131.904C232.97 134.133 234.589 133.551 235.489 130.741Z"
            fill={selectedMuscles?.includes("triceps") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M149.145 104.961C149.145 104.67 149.145 104.282 149.055 103.992C148.785 103.507 148.515 102.635 148.245 102.635C147.616 102.538 146.806 102.829 146.446 103.313C142.579 108.644 138.711 113.974 137.902 120.855C137.722 122.115 137.542 123.278 137.272 125.119C137.362 126.476 137.542 128.318 137.722 130.256C137.722 130.45 137.812 130.644 137.902 130.838C138.801 133.648 140.42 134.23 142.489 132.001C146.356 127.93 148.875 123.181 149.145 117.463C149.235 113.199 149.145 109.031 149.145 104.961Z"
            fill={selectedMuscles?.includes("triceps") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M234.499 105.736C233.329 105.446 232.25 105.155 230.721 104.864C230.991 105.736 231.081 106.221 231.351 106.705C232.61 109.419 234.049 112.036 235.128 114.846C236.747 118.82 239.895 121.631 243.403 124.247C245.382 125.701 247.18 125.12 247.72 122.891C247.9 122.406 247.99 121.824 247.9 121.34C247.54 119.111 247.36 116.979 246.731 114.846C245.921 112.23 244.842 109.71 243.853 107.19C243.583 106.512 243.133 105.93 242.054 106.124C239.445 106.705 236.927 106.318 234.499 105.736Z"
            fill={selectedMuscles?.includes("triceps") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
          <Path
            d="M246.101 125.895C245.022 125.798 243.853 125.507 242.863 125.022C241.154 124.15 239.625 123.181 237.736 122.018C237.736 122.502 237.826 122.696 237.826 122.987C237.826 124.925 238.006 126.864 237.736 128.802C237.467 131.128 236.477 133.163 233.869 134.52L233.779 134.617C233.599 134.617 233.329 134.617 232.79 134.617C231.71 134.52 230.361 133.26 229.552 132.388C229.372 132.194 229.192 132 229.012 131.806C228.472 131.225 227.933 130.643 227.483 130.159C227.393 130.256 227.303 130.256 227.123 130.353C227.213 130.547 227.213 130.74 227.303 130.934C229.372 135.005 231.351 139.075 233.599 143.049C234.409 144.406 235.848 145.666 237.107 146.829C237.467 147.216 238.456 147.216 239.086 147.313L242.323 147.604C242.683 147.701 243.133 147.604 243.583 147.41C244.932 146.829 246.371 146.344 247.9 146.053C248.889 145.859 249.339 145.472 249.429 144.696C249.699 142.37 250.059 140.141 250.059 137.815C249.969 134.229 249.429 130.74 248.08 127.348C247.63 126.476 247.18 125.991 246.101 125.895Z"
            fill={selectedMuscles?.includes("triceps") ? COLORS.red : "#C1C1C1"}
            stroke="#231F20"
            stroke-width="0.5"
            stroke-miterlimit="10"
          />
        </G>

        {showLabel && (
          <>
            <G>
              // Glutes
              <Line
                x1="162.361"
                y1="215.346"
                x2="114.361"
                y2="265.346"
                stroke="#D9D9D9"
              />
              <Rect
                onPress={() => {
                  if (setSelectedMuscles) {
                    setSelectedMuscles((prev: any) => {
                      if (prev.back.includes("glutes")) {
                        return {
                          ...prev,
                          back: prev.back.filter(
                            (item: any) => item !== "glutes"
                          ),
                        };
                      } else {
                        return {
                          ...prev,
                          back: [...prev.back, "glutes"],
                        };
                      }
                    });
                  }
                }}
                width="100"
                height="28"
                rx="5"
                transform="matrix(1 0 0 -1 16 278)"
                fill="#D9D9D9"
              />
              <Circle
                cx="3.5"
                cy="3.5"
                r="3.5"
                transform="matrix(1 0 0 -1 106 268)"
                fill="#373737"
              />
              <Text
                x="25"
                y="268"
                fontSize={`${responsiveFontSize(12)}`}
                fontWeight="bold"
                fill="black"
              >
                Glutes (10kg)
              </Text>
            </G>

            <G>
              // Triceps
              <Line
                x1="260.293"
                y1="110.4053"
                x2="250.293"
                y2="124.405"
                stroke="#D9D9D9"
              />
              <Rect
                onPress={() => {
                  if (setSelectedMuscles) {
                    setSelectedMuscles((prev: any) => {
                      if (prev.back.includes("triceps")) {
                        return {
                          ...prev,
                          back: prev.back.filter(
                            (item: any) => item !== "triceps"
                          ),
                        };
                      } else {
                        return {
                          ...prev,
                          back: [...prev.back, "triceps"],
                        };
                      }
                    });
                  }
                }}
                x="260"
                y="90"
                width="100"
                height="28"
                rx="5"
                fill="#D9D9D9"
              />
              <Circle cx="268.5" cy="103.5" r="3.5" fill="#373737" />
              <Text
                x="275"
                y="108"
                fontSize={`${responsiveFontSize(12)}`}
                fontWeight="bold"
                fill="black"
              >
                Triceps (12kg)
              </Text>
            </G>

            <G filter="url(#filter0_d_13_197)">
              // Hamstrings
              <Rect
                onPress={() => {
                  if (setSelectedMuscles) {
                    setSelectedMuscles((prev: any) => {
                      if (prev.back.includes("hamstrings")) {
                        return {
                          ...prev,
                          back: prev.back.filter(
                            (item: any) => item !== "hamstrings"
                          ),
                        };
                      } else {
                        return {
                          ...prev,
                          back: [...prev.back, "hamstrings"],
                        };
                      }
                    });
                  }
                }}
                x="245"
                y="256"
                width="105"
                height="28"
                rx="5"
                fill="#D9D9D9"
              />
              <Circle cx="253.5" cy="269.5" r="3.5" fill="#373737" />
              <Line
                x1="245.884"
                y1="268.486"
                x2="203.884"
                y2="252.486"
                stroke="#D9D9D9"
              />
              <Text
                x="260.5"
                y="273.5"
                fontSize={`${responsiveFontSize(12)}`}
                fontWeight="bold"
                fill="black"
              >
                Hamstr.. (30kg)
              </Text>
            </G>

            <G filter="url(#filter1_d_13_197)">
              // Calf
              <Rect
                onPress={() => {
                  if (setSelectedMuscles) {
                    setSelectedMuscles((prev: any) => {
                      if (prev.back.includes("calf")) {
                        return {
                          ...prev,
                          back: prev.back.filter(
                            (item: any) => item !== "calf"
                          ),
                        };
                      } else {
                        return {
                          ...prev,
                          back: [...prev.back, "calf"],
                        };
                      }
                    });
                  }
                }}
                x="251"
                y="301"
                width="85"
                height="28"
                rx="5"
                fill="#D9D9D9"
              />
              <Circle cx="257.5" cy="314.5" r="3.5" fill="#373737" />
              <Line
                x1="255.311"
                y1="317.392"
                x2="226.311"
                y2="340.392"
                stroke="#D9D9D9"
              />
              <Text
                x="268.5"
                y="318.5"
                fontSize={`${responsiveFontSize(12)}`}
                fontWeight="bold"
                fill="black"
              >
                Calf (20kg)
              </Text>
            </G>

            <G>
              // Back
              <Rect
                onPress={() => {
                  if (setSelectedMuscles) {
                    setSelectedMuscles((prev: any) => {
                      if (prev.back.includes("back")) {
                        return {
                          ...prev,
                          back: prev.back.filter(
                            (item: any) => item !== "back"
                          ),
                        };
                      } else {
                        return {
                          ...prev,
                          back: [...prev.back, "back"],
                        };
                      }
                    });
                  }
                }}
                x="5"
                y="50.8073"
                width="100"
                height="28"
                rx="5"
                fill="#D9D9D9"
              />
              <Path d="M180.5 106L95.5 65" stroke="#D9D9D9" />
              <Circle cx="91.5" cy="64.3073" r="3.5" fill="#373737" />
              <Text
                x="15.5"
                y="68.5"
                fontSize={`${responsiveFontSize(12)}`}
                fontWeight="bold"
                fill="black"
              >
                Back (25kg)
              </Text>
            </G>
          </>
        )}
      </Svg>
    </View>
  );
};

export default SkeletonBack;

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignItems: "center",
    position: "relative",
  },
});
