import React from "react";
import { View, TouchableOpacity, StyleSheet } from "react-native";
import { CustomText } from "../CustomText";
import { useAppSelector } from "../../Redux/store";
import { 
  selectFrontMuscles, 
  selectBackMuscles, 
  MuscleSelection,
  SpecificMuscle 
} from "../../Redux/slices/muscleSlice";
import COLORS from "../../Utilities/Colors";
import { horizontalScale, verticalScale, wp } from "../../Utilities/Metrics";
import SkeletonFront from "../Cards/SkeletonFront";
import SkeletonBack from "../Cards/SkeletonBack";

interface MuscleSelectorProps {
  selectedMuscles: MuscleSelection;
  onMuscleSelectionChange: (muscles: MuscleSelection) => void;
  title?: string;
  showLabels?: boolean;
  onClickFront?: () => void;
  onClickBack?: () => void;
}

const MuscleSelector: React.FC<MuscleSelectorProps> = ({
  selectedMuscles,
  onMuscleSelectionChange,
  title = "Select Muscles",
  showLabels = true,
  onClickFront,
  onClickBack,
}) => {
  const frontMuscles = useAppSelector(selectFrontMuscles);
  const backMuscles = useAppSelector(selectBackMuscles);

  const handleMuscleToggle = (muscle: SpecificMuscle, side: "front" | "back") => {
    const currentSideMuscles = selectedMuscles[side];
    const isSelected = currentSideMuscles.includes(muscle);

    const updatedMuscles = {
      ...selectedMuscles,
      [side]: isSelected
        ? currentSideMuscles.filter((m) => m !== muscle)
        : [...currentSideMuscles, muscle],
    };

    onMuscleSelectionChange(updatedMuscles);
  };

  const setSelectedMuscles = (updateFunction: (prev: MuscleSelection) => MuscleSelection) => {
    const updatedMuscles = updateFunction(selectedMuscles);
    onMuscleSelectionChange(updatedMuscles);
  };

  return (
    <View style={styles.container}>
      {title && (
        <CustomText fontSize={24} fontFamily="bold" color={COLORS.white} style={styles.title}>
          {title}
        </CustomText>
      )}

      <CustomText
        fontSize={20}
        style={styles.subtitle}
        color={COLORS.white}
      >
        Select muscles
      </CustomText>

      <View style={styles.skeletonContainer}>
        <TouchableOpacity onPress={onClickFront} style={styles.skeletonWrapper}>
          <CustomText color={COLORS.white} style={styles.skeletonLabel}>
            Front
          </CustomText>
          <SkeletonFront
            showLabel={showLabels}
            width={wp(40)}
            height={verticalScale(200)}
            containerWidth={wp(45)}
            selectedMuscles={selectedMuscles.front}
            setSelectedMuscles={setSelectedMuscles}
          />
        </TouchableOpacity>

        <TouchableOpacity onPress={onClickBack} style={styles.skeletonWrapper}>
          <CustomText color={COLORS.white} style={styles.skeletonLabel}>
            Back
          </CustomText>
          <SkeletonBack
            showLabel={showLabels}
            width={wp(40)}
            height={verticalScale(200)}
            containerWidth={wp(45)}
            selectedMuscles={selectedMuscles.back}
            setSelectedMuscles={setSelectedMuscles}
          />
        </TouchableOpacity>
      </View>

      {/* Display selected muscles */}
      {/* {(selectedMuscles.front.length > 0 || selectedMuscles.back.length > 0) && (
        <View style={styles.selectedMusclesContainer}>
          <CustomText fontSize={16} fontFamily="medium" color={COLORS.white}>
            Selected Muscles:
          </CustomText>
          <View style={styles.muscleTagsContainer}>
            {selectedMuscles.front.map((muscle) => {
              const muscleData = frontMuscles.find((m) => m.name === muscle);
              return (
                <TouchableOpacity
                  key={`front-${muscle}`}
                  style={styles.muscleTag}
                  onPress={() => handleMuscleToggle(muscle, "front")}
                >
                  <CustomText fontSize={12} color={COLORS.white}>
                    {muscleData?.displayName || muscle}
                  </CustomText>
                </TouchableOpacity>
              );
            })}
            {selectedMuscles.back.map((muscle) => {
              const muscleData = backMuscles.find((m) => m.name === muscle);
              return (
                <TouchableOpacity
                  key={`back-${muscle}`}
                  style={styles.muscleTag}
                  onPress={() => handleMuscleToggle(muscle, "back")}
                >
                  <CustomText fontSize={12} color={COLORS.white}>
                    {muscleData?.displayName || muscle}
                  </CustomText>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      )} */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: verticalScale(20),
  },
  title: {
    marginBottom: verticalScale(10),
  },
  subtitle: {
    textAlign: "center",
    marginVertical: verticalScale(10),
  },
  skeletonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: horizontalScale(10),
  },
  skeletonWrapper: {
    backgroundColor: COLORS.brown,
    padding: verticalScale(15),
    borderRadius: 20,
    width: wp(45),
    alignItems: "center",
    borderWidth: 1,
    borderColor: COLORS.white,
  },
  skeletonLabel: {
    marginBottom: verticalScale(10),
    fontFamily: "medium",
  },
  selectedMusclesContainer: {
    marginTop: verticalScale(20),
    padding: verticalScale(15),
    backgroundColor: COLORS.lightBrown,
    borderRadius: 10,
  },
  muscleTagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: horizontalScale(8),
    marginTop: verticalScale(10),
  },
  muscleTag: {
    backgroundColor: COLORS.yellow,
    paddingHorizontal: horizontalScale(12),
    paddingVertical: verticalScale(6),
    borderRadius: 15,
  },
});

export default MuscleSelector;
