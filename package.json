{"name": "GymLogix", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.2", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-picker/picker": "^2.11.0", "@react-navigation/bottom-tabs": "^7.2.1", "@react-navigation/drawer": "^7.1.2", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.2.1", "@reduxjs/toolkit": "^2.6.0", "axios": "^1.8.1", "dayjs": "^1.11.13", "react": "18.3.1", "react-native": "0.77.1", "react-native-calendars": "^1.1310.0", "react-native-gesture-handler": "^2.24.0", "react-native-gifted-charts": "^1.4.58", "react-native-image-picker": "^8.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-multi-slider": "^0.3.6", "react-native-picker-select": "^9.3.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-reanimated": "^3.17.1", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "4.4.0", "react-native-star-rating-widget": "^1.9.2", "react-native-svg": "^15.11.2", "react-native-svg-transformer": "^1.5.0", "react-native-toast-message": "^2.2.1", "react-redux": "^9.2.0", "rn-range-slider": "^2.2.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.1", "@react-native/eslint-config": "0.77.1", "@react-native/metro-config": "0.77.1", "@react-native/typescript-config": "0.77.1", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-native-multi-slider": "^0.3.5", "@types/react-test-renderer": "^18.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "resolutions": {"react-native-modal@^13.0.1": "patch:react-native-modal@npm%3A13.0.1#./.yarn/patches/react-native-modal-npm-13.0.1-f1a75332f0.patch"}}